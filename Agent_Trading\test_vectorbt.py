#!/usr/bin/env python3
"""
Test VectorBT import to see if it's causing the hang
"""

print("🔄 Testing VectorBT import...")

try:
    import vectorbt as vbt
    print(f"✅ VectorBT imported successfully: {vbt.__version__}")
except Exception as e:
    print(f"❌ VectorBT import failed: {e}")

print("🔄 Testing pandas...")
try:
    import pandas as pd
    print("✅ Pandas works")
except Exception as e:
    print(f"❌ Pandas failed: {e}")

print("🔄 Testing numpy...")
try:
    import numpy as np
    print("✅ NumPy works")
except Exception as e:
    print(f"❌ NumPy failed: {e}")

print("✅ All basic imports complete!")
