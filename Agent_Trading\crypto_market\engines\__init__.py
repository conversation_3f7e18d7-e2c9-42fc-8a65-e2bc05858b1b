"""
Professional Trading System Engines
===================================

Institutional-grade trading system with modular engine architecture:

🏗️ CORE ENGINES:
├── 📊 DATA ENGINE          # Market data pipeline
├── 🧠 SIGNAL ENGINE        # Strategy & indicators  
├── 🎯 EXECUTION ENGINE     # Order management
├── 🛡️ RISK ENGINE          # Risk management
├── 📈 BACKTESTING ENGINE   # Realistic simulation (VectorBT + Backtrader)
└── 📱 MONITORING ENGINE    # Performance tracking

Target: Capture 300-3000 point BTC movements with professional execution
"""

__version__ = "1.0.0"
__author__ = "Professional Trading System"

# Core engine imports
from .data_engine import (
    DatabaseManager,
    MarketDataFeed,
    SimpleDataFetcher,
    DataNormalizer,
    TimeframeManager,
    DataValidator
)

from .Trading_engine import (
    TradingSystem,
    BigTrendVectorBTBacktester,
    DashboardDataLoader,
    generate_enhanced_signals
)

__all__ = [
    # Data Engine
    'DatabaseManager',
    'MarketDataFeed',
    'SimpleDataFetcher',
    'DataNormalizer',
    'TimeframeManager',
    'DataValidator',

    # Trading Engine
    'TradingSystem',
    'BigTrendVectorBTBacktester',
    'DashboardDataLoader',
    'generate_enhanced_signals'
]
