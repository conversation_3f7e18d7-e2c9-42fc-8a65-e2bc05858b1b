#!/usr/bin/env python3
"""
Test the backtester functionality
"""

import sys
import os
sys.path.insert(0, 'Agent_Trading')

import pandas as pd
import numpy as np
from datetime import datetime, <PERSON><PERSON><PERSON>

def create_sample_data():
    """Create sample OHLCV data for testing"""
    print("🔄 Creating sample data...")
    
    # Create 3-minute data
    dates_3m = pd.date_range(start=datetime.now() - timedelta(days=7), periods=2000, freq='3min')
    np.random.seed(42)
    
    # Generate realistic BTC price data
    base_price = 50000
    price_changes = np.random.normal(0, 100, len(dates_3m))
    prices = base_price + np.cumsum(price_changes)
    
    df_3m = pd.DataFrame({
        'open': prices + np.random.normal(0, 50, len(dates_3m)),
        'high': prices + np.abs(np.random.normal(100, 50, len(dates_3m))),
        'low': prices - np.abs(np.random.normal(100, 50, len(dates_3m))),
        'close': prices,
        'volume': np.random.uniform(100, 1000, len(dates_3m))
    }, index=dates_3m)
    
    # Create 15-minute data (every 5th row)
    df_15m = df_3m.iloc[::5].copy()
    
    # Create 1-hour data (every 20th row)
    df_1h = df_3m.iloc[::20].copy()
    
    print(f"✅ Sample data created:")
    print(f"   3m: {len(df_3m)} candles")
    print(f"   15m: {len(df_15m)} candles")
    print(f"   1h: {len(df_1h)} candles")
    
    return df_3m, df_15m, df_1h

def test_backtester():
    """Test the backtester functionality"""
    print("\n🚀 TESTING BIG TREND BACKTESTER")
    print("=" * 50)
    
    try:
        # Import the backtester
        from crypto_market.engines.Trading_engine.backtester_vbt import BigTrendVectorBTBacktester
        print("✅ Backtester imported successfully")
        
        # Create sample data
        df_3m, df_15m, df_1h = create_sample_data()
        
        # Initialize backtester
        backtester = BigTrendVectorBTBacktester(initial_capital=100000)
        print("✅ Backtester initialized")
        
        # Test indicator computation
        print("\n🔄 Testing indicator computation...")
        df_3m_ind = backtester.prepare_data(df_3m, df_15m, df_1h)[0]
        print("✅ Indicators computed successfully")
        print(f"   Indicators: {[col for col in df_3m_ind.columns if col not in ['open', 'high', 'low', 'close', 'volume']]}")
        
        # Test signal generation
        print("\n🔄 Testing signal generation...")
        signals_df = backtester.generate_signals(df_3m_ind, df_15m, df_1h)
        print(f"✅ Signal generation completed: {len(signals_df)} signals")
        
        if len(signals_df) > 0:
            print(f"   Signal types: {signals_df['signal'].value_counts().to_dict()}")
            print(f"   Average R:R: {signals_df['risk_reward'].mean():.2f}")
        
        # Test VectorBT backtest
        if len(signals_df) > 0:
            print("\n🔄 Testing VectorBT backtest...")
            pf = backtester.run_vectorbt_backtest(df_3m_ind, signals_df)
            
            if pf is not None:
                print("✅ VectorBT backtest completed successfully!")
                print(f"   Final portfolio value: ${pf.value.iloc[-1]:,.2f}")
                print(f"   Total return: {pf.total_return():.2%}")
                print(f"   Total trades: {pf.trades.count()}")
                if pf.trades.count() > 0:
                    print(f"   Win rate: {pf.trades.win_rate():.1%}")
            else:
                print("❌ VectorBT backtest failed")
        else:
            print("⚠️ No signals generated - skipping VectorBT test")
        
        print("\n🎯 BACKTESTER TEST COMPLETED SUCCESSFULLY!")
        return True
        
    except Exception as e:
        print(f"❌ Backtester test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_backtester()
    if success:
        print("\n✅ All tests passed! Your backtester is ready for big trend trading!")
    else:
        print("\n❌ Tests failed. Please check the errors above.")
