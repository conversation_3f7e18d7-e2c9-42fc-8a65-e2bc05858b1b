#!/usr/bin/env python3
"""
Test the dashboard parameter fix
"""

import sys
import os
sys.path.insert(0, 'Agent_Trading')

def test_parameter_fix():
    """Test that the parameter conflict is resolved"""
    print("🔍 Testing dashboard parameter fix...")
    
    # Simulate the parameters that were causing the issue
    params = {
        'period_type': 'Recent Days',
        'days': 30,
        'initial_capital': 100000,
        'min_confluence': 4,
        'min_rr': 4.0,
        'max_sl_points': 300
    }
    
    # Test the fix - extract period_type to avoid duplicate parameter
    period_type = params.pop('period_type')
    print(f"✅ Extracted period_type: {period_type}")
    print(f"✅ Remaining params: {list(params.keys())}")
    
    # Simulate the method call that was failing
    def mock_load_backtest_data(period_type, **kwargs):
        """Mock method to test parameter passing"""
        print(f"   period_type: {period_type}")
        print(f"   kwargs: {kwargs}")
        return True
    
    # This should now work without the "multiple values" error
    try:
        result = mock_load_backtest_data(period_type, **params)
        print("✅ Parameter passing test successful!")
        return True
    except TypeError as e:
        print(f"❌ Parameter passing test failed: {e}")
        return False

def test_dashboard_import():
    """Test that dashboard components can be imported"""
    print("\n🔍 Testing dashboard imports...")
    
    try:
        # Test the dashboard imports
        from dashboard.dashboard_imports import get_trading_components
        components = get_trading_components()
        print(f"✅ Dashboard imports successful! Got {len(components)} components")
        print(f"   Components: {[c.__name__ for c in components]}")
        return True
    except Exception as e:
        print(f"❌ Dashboard import failed: {e}")
        return False

def test_backtester_import():
    """Test that the backtester can be imported and initialized"""
    print("\n🔍 Testing backtester import...")
    
    try:
        from crypto_market.engines.Trading_engine.backtester_vbt import BigTrendVectorBTBacktester
        backtester = BigTrendVectorBTBacktester(initial_capital=100000)
        print("✅ Backtester import and initialization successful!")
        print(f"   Initial capital: ${backtester.initial_capital:,}")
        print(f"   Strategy settings: {list(backtester.strategy_settings.keys())}")
        return True
    except Exception as e:
        print(f"❌ Backtester import failed: {e}")
        return False

if __name__ == "__main__":
    print("🚀 TESTING DASHBOARD FIXES")
    print("=" * 50)
    
    success_count = 0
    total_tests = 3
    
    # Run tests
    if test_parameter_fix():
        success_count += 1
    
    if test_dashboard_import():
        success_count += 1
        
    if test_backtester_import():
        success_count += 1
    
    print("\n" + "=" * 50)
    print(f"🎯 TEST RESULTS: {success_count}/{total_tests} tests passed")
    
    if success_count == total_tests:
        print("✅ All tests passed! Dashboard should now work correctly.")
        print("🚀 You can now run your backtest in the dashboard!")
    else:
        print("❌ Some tests failed. Please check the errors above.")
