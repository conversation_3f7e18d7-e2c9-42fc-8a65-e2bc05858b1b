"""
Crypto Market Trading System
===========================

Professional cryptocurrency trading system for BTC on Delta Exchange.
Designed to capture big trends (300-3000 points) with institutional-grade execution.

Architecture:
- Data Engine: Market data pipeline and management
- Trading Engine: Signal generation and backtesting
- Professional dashboard interface

Target: Exceptional returns through big trend capture
"""

# Import main engines
from .engines.data_engine import (
    DatabaseManager,
    MarketDataFeed,
    SimpleDataFetcher,
    DataNormalizer,
    TimeframeManager,
    DataValidator
)

from .engines.Trading_engine import (
    TradingSystem,
    BigTrendVectorBTBacktester,
    DashboardDataLoader,
    generate_enhanced_signals
)

__all__ = [
    # Data Engine
    'DatabaseManager',
    'MarketDataFeed', 
    'SimpleDataFetcher',
    'DataNormalizer',
    'TimeframeManager',
    'DataValidator',
    
    # Trading Engine
    'TradingSystem',
    'BigTrendVectorBTBacktester',
    'DashboardDataLoader',
    'generate_enhanced_signals'
]

__version__ = "1.0.0"
