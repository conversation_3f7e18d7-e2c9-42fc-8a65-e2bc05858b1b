#!/usr/bin/env python3
"""
Comprehensive Project Issue Diagnosis
=====================================

Diagnose all issues in the trading system project.
"""

import sys
import os
from pathlib import Path
import importlib.util

print("🔍 COMPREHENSIVE PROJECT DIAGNOSIS")
print("=" * 50)

# 1. Check Python environment
print("\n1️⃣ PYTHON ENVIRONMENT")
print(f"Python version: {sys.version}")
print(f"Python executable: {sys.executable}")
print(f"Current working directory: {os.getcwd()}")

# 2. Check critical dependencies
print("\n2️⃣ CRITICAL DEPENDENCIES")
critical_deps = ['pandas', 'numpy', 'talib', 'vectorbt', 'streamlit', 'sqlite3']

for dep in critical_deps:
    try:
        if dep == 'sqlite3':
            import sqlite3
            print(f"✅ {dep}: {sqlite3.sqlite_version}")
        else:
            module = __import__(dep)
            version = getattr(module, '__version__', 'unknown')
            print(f"✅ {dep}: {version}")
    except ImportError as e:
        print(f"❌ {dep}: MISSING - {e}")
    except Exception as e:
        print(f"⚠️ {dep}: ERROR - {e}")

# 3. Check project structure
print("\n3️⃣ PROJECT STRUCTURE")
project_root = Path(__file__).parent
critical_paths = [
    'config.json',
    'crypto_market/__init__.py',
    'crypto_market/engines/__init__.py',
    'crypto_market/engines/data_engine/__init__.py',
    'crypto_market/engines/Trading_engine/__init__.py',
    'crypto_market/engines/Trading_engine/enhanced_indicator.py',
    'crypto_market/engines/Trading_engine/enhanced_signal_helpers.py',
    'crypto_market/engines/Trading_engine/big_trend_system.py',
    'crypto_market/engines/Trading_engine/backtester_vbt.py',
    'crypto_market/engines/Trading_engine/data_loader.py',
    'dashboard/dashboard_imports.py',
    'core/config_manager.py'
]

for path in critical_paths:
    full_path = project_root / path
    if full_path.exists():
        print(f"✅ {path}")
    else:
        print(f"❌ {path}: MISSING")

# 4. Test imports step by step
print("\n4️⃣ IMPORT TESTING")

# Test basic imports
try:
    import pandas as pd
    import numpy as np
    print("✅ Basic imports (pandas, numpy)")
except Exception as e:
    print(f"❌ Basic imports failed: {e}")

# Test TA-Lib
try:
    import talib
    print(f"✅ TA-Lib: {talib.__version__}")
except Exception as e:
    print(f"❌ TA-Lib failed: {e}")

# Test VectorBT
try:
    import vectorbt as vbt
    print(f"✅ VectorBT: {vbt.__version__}")
except Exception as e:
    print(f"❌ VectorBT failed: {e}")

# Test our package imports
try:
    from crypto_market.engines.data_engine import DatabaseManager
    print("✅ Data engine import")
except Exception as e:
    print(f"❌ Data engine import failed: {e}")

try:
    from crypto_market.engines.Trading_engine.enhanced_signal_helpers import SignalStrength
    print("✅ Enhanced signal helpers import")
except Exception as e:
    print(f"❌ Enhanced signal helpers import failed: {e}")

try:
    from crypto_market.engines.Trading_engine.enhanced_indicator import generate_enhanced_signals
    print("✅ Enhanced indicator import")
except Exception as e:
    print(f"❌ Enhanced indicator import failed: {e}")

try:
    from crypto_market.engines.Trading_engine import TradingSystem
    print("✅ Trading engine package import")
except Exception as e:
    print(f"❌ Trading engine package import failed: {e}")

try:
    from dashboard.dashboard_imports import get_trading_components
    print("✅ Dashboard imports")
except Exception as e:
    print(f"❌ Dashboard imports failed: {e}")

# 5. Check configuration
print("\n5️⃣ CONFIGURATION")
config_path = project_root / 'config.json'
if config_path.exists():
    try:
        import json
        with open(config_path) as f:
            config = json.load(f)
        print("✅ Config file loads successfully")
        print(f"   Database path: {config.get('database', {}).get('path', 'NOT SET')}")
    except Exception as e:
        print(f"❌ Config file error: {e}")
else:
    print("❌ Config file missing")

# 6. Check database
print("\n6️⃣ DATABASE")
try:
    from core.config_manager import ConfigManager
    from crypto_market.engines.data_engine.database_manager import DatabaseManager
    
    config_manager = ConfigManager(str(config_path))
    db_manager = DatabaseManager(config_manager)
    
    # Test database connection
    with db_manager.get_connection() as conn:
        cursor = conn.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = [row[0] for row in cursor.fetchall()]
        print(f"✅ Database connection successful")
        print(f"   Tables: {tables}")
        
except Exception as e:
    print(f"❌ Database error: {e}")

print("\n🎯 DIAGNOSIS COMPLETE!")
print("=" * 50)
