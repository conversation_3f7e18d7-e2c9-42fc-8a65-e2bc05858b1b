"""
Dashboard Imports - Simple and Direct
====================================
"""

import sys
from pathlib import Path

# Add trading engine to path
dashboard_dir = Path(__file__).parent.absolute()
agent_trading_dir = dashboard_dir.parent.absolute()
trading_engine_dir = agent_trading_dir / "crypto_market" / "engines" / "Trading_engine"
sys.path.insert(0, str(trading_engine_dir))

# Direct imports - no complications
from backtester_vbt import BigTrendVectorBTBacktester
from enhanced_indicator import compute_enhanced_indicators, generate_enhanced_signals
from big_trend_system import BigTrendTradingSystem

# Data loader
sys.path.insert(0, str(agent_trading_dir))
from core.config_manager import ConfigManager
from crypto_market.engines.data_engine.database_manager import DatabaseManager

class DashboardDataLoader:
    """Simple data loader for dashboard"""

    def __init__(self):
        config_path = str(agent_trading_dir / "config.json")
        self.config = ConfigManager(config_path)
        self.db_manager = DatabaseManager(self.config)

    def load_recent_data(self, days=30):
        """Load recent data"""
        from datetime import datetime, timedelta
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)

        df_3m = self.db_manager.get_market_data('3m', start_date, end_date)
        df_15m = self.db_manager.get_market_data('15m', start_date, end_date)
        df_1h = self.db_manager.get_market_data('1h', start_date, end_date)

        return df_3m, df_15m, df_1h

def get_trading_components():
    """Get trading components"""
    return DashboardDataLoader, BigTrendVectorBTBacktester
