#!/usr/bin/env python3
"""Test Trading Engine imports step by step"""

print("🔍 Testing Trading Engine imports...")

# Test 1: Enhanced signal helpers
try:
    from crypto_market.engines.Trading_engine.enhanced_signal_helpers import SignalStrength
    print("✅ enhanced_signal_helpers works")
except Exception as e:
    print(f"❌ enhanced_signal_helpers failed: {e}")

# Test 2: Enhanced indicator
try:
    from crypto_market.engines.Trading_engine.enhanced_indicator import generate_enhanced_signals
    print("✅ enhanced_indicator works")
except Exception as e:
    print(f"❌ enhanced_indicator failed: {e}")
    import traceback
    traceback.print_exc()

# Test 3: Data loader
try:
    from crypto_market.engines.Trading_engine.data_loader import TradingDataLoader
    print("✅ data_loader works")
except Exception as e:
    print(f"❌ data_loader failed: {e}")
    import traceback
    traceback.print_exc()

# Test 4: Trading engine package
try:
    from crypto_market.engines.Trading_engine import TradingSystem
    print("✅ Trading_engine package works")
except Exception as e:
    print(f"❌ Trading_engine package failed: {e}")
    import traceback
    traceback.print_exc()

print("🎯 Trading Engine test complete!")
