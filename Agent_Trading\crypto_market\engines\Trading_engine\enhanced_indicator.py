"""
Enhanced Professional Trading Indicator System
==============================================

Advanced indicator system with:
- Trade logging and tracking
- Dynamic risk allocation
- Trailing stop methodology
- Confluence validation
- False signal learning
- Performance metrics
"""

# Try to import TA-LIB, fallback to manual calculations if not available
try:
    import talib
    TALIB_AVAILABLE = True
    print("✅ TA-LIB imported successfully")
except ImportError:
    TALIB_AVAILABLE = False
    print("⚠️ TA-LIB not available, using manual calculations")

import pandas as pd
import numpy as np
from datetime import datetime
from typing import Dict, List, Optional, Tuple
import logging
from dataclasses import dataclass, asdict
import json
from pathlib import Path

# Import helper functions and enums
try:
    from .enhanced_signal_helpers import (
        SignalStrength,
        TrailingMethod,
        calculate_confluence,
        classify_signal_strength,
        calculate_stop_take_profit,
        calculate_dynamic_risk,
        determine_trailing_method,
        calculate_confidence_score
    )
except ImportError:
    # Fallback imports for when running as standalone
    from enhanced_signal_helpers import (
        SignalStrength,
        TrailingMethod,
        calculate_confluence,
        classify_signal_strength,
        calculate_stop_take_profit,
        calculate_dynamic_risk,
        determine_trailing_method,
        calculate_confidence_score
    )

# Setup professional logging (simplified for import compatibility)
logger = logging.getLogger(__name__)
if not logger.handlers:
    handler = logging.StreamHandler()
    handler.setFormatter(logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s'))
    logger.addHandler(handler)
    logger.setLevel(logging.INFO)

# Enums imported from enhanced_signal_helpers

@dataclass
class TradeSignal:
    """Professional trade signal structure"""
    signal: str
    strength: SignalStrength
    entry_price: float
    stop_loss: float
    take_profit: float
    buy_score: float
    sell_score: float
    confluence_count: int
    risk_reward_ratio: float
    confidence: float
    timestamp: datetime
    timeframe_alignment: Dict[str, bool]
    trailing_method: TrailingMethod
    risk_allocation: float
    notes: str = ""

    def to_dict(self):
        """Convert to dictionary for logging"""
        data = asdict(self)
        data['strength'] = self.strength.value
        data['trailing_method'] = self.trailing_method.value
        data['timestamp'] = self.timestamp.isoformat()
        return data

class TradingMetrics:
    """Track trading performance metrics"""

    def __init__(self):
        self.signals_log = []
        self.false_signals = []
        # Disable file operations during import to prevent hanging
        self.metrics_file = None
        # File operations will be enabled when first used

    def log_signal(self, signal: TradeSignal):
        """Log trading signal"""
        self.signals_log.append(signal.to_dict())
        logger.info(f"Signal Generated: {signal.signal} | Strength: {signal.strength.name} | R:R: {signal.risk_reward_ratio:.2f}")
        self.save_metrics()

    def log_false_signal(self, signal: TradeSignal, reason: str):
        """Log false signals for learning"""
        false_signal = signal.to_dict()
        false_signal['false_reason'] = reason
        self.false_signals.append(false_signal)
        logger.warning(f"False Signal Detected: {reason}")
        self.save_metrics()

    def get_performance_stats(self) -> Dict:
        """Calculate performance statistics"""
        if not self.signals_log:
            return {}

        total_signals = len(self.signals_log)
        false_signals = len(self.false_signals)

        # Calculate win rate (assuming false signals are losses)
        win_rate = max(0, (total_signals - false_signals) / total_signals) if total_signals > 0 else 0

        # Average R:R ratio
        avg_rr = np.mean([s['risk_reward_ratio'] for s in self.signals_log])

        # Signal strength distribution
        strength_dist = {}
        for signal in self.signals_log:
            strength = signal['strength']
            strength_dist[strength] = strength_dist.get(strength, 0) + 1

        return {
            'total_signals': total_signals,
            'false_signals': false_signals,
            'win_rate': win_rate,
            'avg_risk_reward': avg_rr,
            'strength_distribution': strength_dist,
            'expectancy': (win_rate * avg_rr) - ((1 - win_rate) * 1)
        }

    def save_metrics(self):
        """Save metrics to file"""
        if self.metrics_file is None:
            return  # Skip file operations if not available

        try:
            data = {
                'signals_log': self.signals_log,
                'false_signals': self.false_signals,
                'performance_stats': self.get_performance_stats()
            }
            with open(self.metrics_file, 'w') as f:
                json.dump(data, f, indent=2)
        except Exception as e:
            logger.warning(f"Could not save metrics: {e}")

    def load_metrics(self):
        """Load metrics from file"""
        if self.metrics_file is None:
            return  # Skip file operations if not available

        try:
            if self.metrics_file.exists():
                with open(self.metrics_file, 'r') as f:
                    data = json.load(f)
                    self.signals_log = data.get('signals_log', [])
                    self.false_signals = data.get('false_signals', [])
        except Exception as e:
            logger.warning(f"Could not load metrics: {e}")

# Global metrics tracker (lazy initialization to prevent import hanging)
metrics_tracker = None

def get_metrics_tracker():
    """Get or create metrics tracker (lazy initialization)"""
    global metrics_tracker
    if metrics_tracker is None:
        metrics_tracker = TradingMetrics()
    return metrics_tracker

# Manual indicator calculations (fallback when TA-LIB not available)
def manual_sma(series, period):
    """Simple Moving Average"""
    return series.rolling(window=period).mean()

def manual_ema(series, period):
    """Exponential Moving Average"""
    return series.ewm(span=period).mean()

def manual_rsi(series, period=14):
    """Relative Strength Index"""
    delta = series.diff()
    gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
    loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
    rs = gain / loss
    return 100 - (100 / (1 + rs))

def manual_macd(series, fast=12, slow=26, signal=9):
    """MACD indicator"""
    ema_fast = manual_ema(series, fast)
    ema_slow = manual_ema(series, slow)
    macd_line = ema_fast - ema_slow
    signal_line = manual_ema(macd_line, signal)
    histogram = macd_line - signal_line
    return macd_line, signal_line, histogram

def manual_bbands(series, period=20, std_dev=2):
    """Bollinger Bands"""
    sma = manual_sma(series, period)
    std = series.rolling(window=period).std()
    upper = sma + (std * std_dev)
    lower = sma - (std * std_dev)
    return upper, sma, lower

def manual_atr(high, low, close, period=14):
    """Average True Range"""
    tr1 = high - low
    tr2 = abs(high - close.shift())
    tr3 = abs(low - close.shift())
    tr = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
    return tr.rolling(window=period).mean()

def manual_adx(high, low, close, period=14):
    """Average Directional Index (simplified)"""
    # Simplified ADX calculation
    tr = manual_atr(high, low, close, 1)
    plus_dm = (high - high.shift()).where((high - high.shift()) > (low.shift() - low), 0)
    minus_dm = (low.shift() - low).where((low.shift() - low) > (high - high.shift()), 0)

    plus_di = 100 * (plus_dm.rolling(period).mean() / tr.rolling(period).mean())
    minus_di = 100 * (minus_dm.rolling(period).mean() / tr.rolling(period).mean())

    dx = 100 * abs(plus_di - minus_di) / (plus_di + minus_di)
    adx = dx.rolling(period).mean()

    return adx, plus_di, minus_di

def manual_obv(close, volume):
    """On Balance Volume"""
    obv = pd.Series(index=close.index, dtype=float)
    obv.iloc[0] = volume.iloc[0]

    for i in range(1, len(close)):
        if close.iloc[i] > close.iloc[i-1]:
            obv.iloc[i] = obv.iloc[i-1] + volume.iloc[i]
        elif close.iloc[i] < close.iloc[i-1]:
            obv.iloc[i] = obv.iloc[i-1] - volume.iloc[i]
        else:
            obv.iloc[i] = obv.iloc[i-1]

    return obv

def compute_enhanced_indicators(
    df: pd.DataFrame,
    indicators: list = None,
    settings: dict = None
) -> pd.DataFrame:
    """Enhanced indicator computation with additional features"""

    # Handle different index types
    if 'timestamp' in df.columns:
        df = df.sort_values('timestamp').copy()
    else:
        df = df.copy()  # Assume index is already sorted

    if indicators is None:
        indicators = [
            'macd', 'rsi', 'adx', 'di', 'sma', 'bbands',
            'atr', 'avg_volume', 'obv', 'ema', 'hma', 'supertrend',
            'support_resistance', 'volatility_regime'
        ]

    if settings is None:
        settings = {
            'sma': [50, 200],
            'ema': [9, 21],
            'rsi': 14,
            'rsi_buy_threshold': 30,
            'rsi_sell_threshold': 70,
            'adx': 14,
            'bbands': 20,
            'atr': 14,
            'hma': [9],
            'avg_volume': 20,
            'supertrend': {'period': 10, 'multiplier': 3},
            'volatility_lookback': 20
        }

    # Enhanced indicators with TA-LIB or manual fallback
    if 'macd' in indicators:
        if TALIB_AVAILABLE:
            df['macd'], df['macd_signal'], df['macd_hist'] = talib.MACD(df['close'])
        else:
            df['macd'], df['macd_signal'], df['macd_hist'] = manual_macd(df['close'])

    if 'rsi' in indicators:
        if TALIB_AVAILABLE:
            df['rsi'] = talib.RSI(df['close'], timeperiod=settings.get('rsi', 14))
        else:
            df['rsi'] = manual_rsi(df['close'], settings.get('rsi', 14))

    if 'adx' in indicators or 'di' in indicators:
        adx_period = settings.get('adx', 14)
        if TALIB_AVAILABLE:
            if 'adx' in indicators:
                df['adx'] = talib.ADX(df['high'], df['low'], df['close'], timeperiod=adx_period)
            if 'di' in indicators:
                df['plus_di'] = talib.PLUS_DI(df['high'], df['low'], df['close'], timeperiod=adx_period)
                df['minus_di'] = talib.MINUS_DI(df['high'], df['low'], df['close'], timeperiod=adx_period)
        else:
            adx, plus_di, minus_di = manual_adx(df['high'], df['low'], df['close'], adx_period)
            if 'adx' in indicators:
                df['adx'] = adx
            if 'di' in indicators:
                df['plus_di'] = plus_di
                df['minus_di'] = minus_di

    if 'sma' in indicators:
        for period in settings.get('sma', [50, 200]):
            if TALIB_AVAILABLE:
                df[f'sma_{period}'] = talib.SMA(df['close'], timeperiod=period)
            else:
                df[f'sma_{period}'] = manual_sma(df['close'], period)

    if 'ema' in indicators:
        for period in settings.get('ema', [9, 21]):
            if TALIB_AVAILABLE:
                df[f'ema_{period}'] = talib.EMA(df['close'], timeperiod=period)
            else:
                df[f'ema_{period}'] = manual_ema(df['close'], period)

    if 'bbands' in indicators:
        bb_period = settings.get('bbands', 20)
        if TALIB_AVAILABLE:
            df['bb_upper'], df['bb_middle'], df['bb_lower'] = talib.BBANDS(df['close'], timeperiod=bb_period)
        else:
            df['bb_upper'], df['bb_middle'], df['bb_lower'] = manual_bbands(df['close'], bb_period)

    if 'atr' in indicators:
        atr_period = settings.get('atr', 14)
        if TALIB_AVAILABLE:
            df['atr'] = talib.ATR(df['high'], df['low'], df['close'], timeperiod=atr_period)
        else:
            df['atr'] = manual_atr(df['high'], df['low'], df['close'], atr_period)

    if 'obv' in indicators:
        if TALIB_AVAILABLE:
            df['obv'] = talib.OBV(df['close'], df['volume'])
        else:
            df['obv'] = manual_obv(df['close'], df['volume'])

    if 'avg_volume' in indicators:
        vol_period = settings.get('avg_volume', 20)
        df['avg_volume'] = df['volume'].rolling(window=vol_period).mean()

    # Enhanced indicators
    if 'support_resistance' in indicators:
        df = add_support_resistance(df)

    if 'volatility_regime' in indicators:
        df = add_volatility_regime(df, settings.get('volatility_lookback', 20))

    # HMA and Supertrend (keeping your original implementation)
    if 'hma' in indicators:
        def WMA(series, period):
            weights = np.arange(1, period + 1)
            return series.rolling(period).apply(lambda prices: np.dot(prices, weights) / weights.sum(), raw=True)

        def HMA(series, period):
            half = int(period / 2)
            sqrt = int(np.sqrt(period))
            wma_half = WMA(series, half)
            wma_full = WMA(series, period)
            raw_hma = 2 * wma_half - wma_full
            return WMA(raw_hma, sqrt)

        for period in settings.get('hma', [9]):
            df[f'hma_{period}'] = HMA(df['close'], period)

    if 'supertrend' in indicators:
        period = settings['supertrend'].get('period', 10)
        multiplier = settings['supertrend'].get('multiplier', 3)

        # Use manual ATR calculation for supertrend
        if TALIB_AVAILABLE:
            atr = talib.ATR(df['high'], df['low'], df['close'], timeperiod=period)
        else:
            atr = manual_atr(df['high'], df['low'], df['close'], period)

        hl2 = (df['high'] + df['low']) / 2
        upperband = hl2 + (multiplier * atr)
        lowerband = hl2 - (multiplier * atr)

        supertrend = pd.Series(index=df.index, dtype=bool)
        direction = pd.Series(index=df.index, dtype=int)

        final_upperband = upperband.copy()
        final_lowerband = lowerband.copy()

        for i in range(1, len(df)):
            if df['close'][i - 1] > final_upperband[i - 1]:
                final_upperband[i] = min(upperband[i], final_upperband[i - 1])
            else:
                final_upperband[i] = upperband[i]

            if df['close'][i - 1] < final_lowerband[i - 1]:
                final_lowerband[i] = max(lowerband[i], final_lowerband[i - 1])
            else:
                final_lowerband[i] = lowerband[i]

            if df['close'][i] > final_upperband[i - 1]:
                supertrend[i] = True
            elif df['close'][i] < final_lowerband[i - 1]:
                supertrend[i] = False
            else:
                supertrend[i] = supertrend[i - 1] if i > 0 else True

            direction[i] = 1 if supertrend[i] else -1

        df['supertrend'] = direction

    df = df.dropna().reset_index(drop=True)
    return df

def add_support_resistance(df: pd.DataFrame, window: int = 20) -> pd.DataFrame:
    """Add support and resistance levels"""
    df['resistance'] = df['high'].rolling(window=window).max()
    df['support'] = df['low'].rolling(window=window).min()

    # Distance from support/resistance
    df['dist_from_resistance'] = (df['resistance'] - df['close']) / df['close']
    df['dist_from_support'] = (df['close'] - df['support']) / df['close']

    return df

def add_volatility_regime(df: pd.DataFrame, lookback: int = 20) -> pd.DataFrame:
    """Add volatility regime classification"""
    df['volatility'] = df['close'].pct_change().rolling(window=lookback).std()
    df['vol_percentile'] = df['volatility'].rolling(window=lookback*5).rank(pct=True)

    # Classify volatility regime
    df['vol_regime'] = pd.cut(df['vol_percentile'],
                             bins=[0, 0.33, 0.66, 1.0],
                             labels=['low', 'medium', 'high'])

    return df

def generate_enhanced_signals(
    df_3m: pd.DataFrame,
    df_15m: pd.DataFrame,
    df_1h: pd.DataFrame,
    settings: dict = None
) -> Optional[TradeSignal]:
    """
    Enhanced signal generation with professional features:
    - Multi-timeframe confluence
    - Dynamic risk allocation
    - Trailing stop methodology
    - Signal strength classification
    """

    if settings is None:
        settings = {
            # BIG TREND CAPTURE SETTINGS
            'min_confluence': 4,           # Higher quality for big moves
            'base_risk': 0.015,           # 1.5% base risk
            'max_risk': 0.03,             # 3% max risk for big moves
            'min_rr': 4.0,                # Minimum 1:4 R:R before trailing
            'target_rr': 5.0,             # Target 1:5 R:R
            'big_trend_rr': 15.0,         # Big trend target (1:15 = 3000 points)

            # STOP LOSS SETTINGS (200-300 points max)
            'max_sl_points': 300,         # Maximum 300 point stop loss
            'min_sl_points': 150,         # Minimum 150 point stop loss
            'atr_sl_multiplier': 1.5,     # Conservative ATR multiplier

            # DAILY LIMITS
            'max_trades_per_day': 3,      # Max 3 trades per day
            'max_sl_per_day': 3,          # Max 3 stop losses per day

            # TRAILING SETTINGS
            'start_trailing_at_rr': 4.0,  # Start trailing only after 1:4
            'trailing_step': 200,         # Trail in 200 point steps
            'trailing_atr_mult': 1.0      # Tight trailing after target hit
        }

    # Get latest data points
    latest_3m = df_3m.iloc[-1]
    latest_15m = df_15m.iloc[-1]
    latest_1h = df_1h.iloc[-1]

    # Calculate confluence signals
    confluence_signals = calculate_confluence(latest_3m, latest_15m, latest_1h)

    if confluence_signals['confluence_count'] < settings['min_confluence']:
        return None

    # Determine signal direction and strength
    signal_direction = "BUY" if confluence_signals['buy_score'] > confluence_signals['sell_score'] else "SELL"
    signal_strength = classify_signal_strength(confluence_signals, settings)

    # Calculate entry, stop loss, and take profit
    entry_price = latest_3m['close']
    stop_loss, take_profit = calculate_stop_take_profit(
        latest_3m, signal_direction, signal_strength, settings
    )

    # Calculate risk-reward ratio
    if signal_direction == "BUY":
        risk = entry_price - stop_loss
        reward = take_profit - entry_price
    else:
        risk = stop_loss - entry_price
        reward = entry_price - take_profit

    risk_reward_ratio = reward / risk if risk > 0 else 0

    # Skip signals with poor R:R
    if risk_reward_ratio < settings['min_rr']:
        return None

    # Dynamic risk allocation based on signal strength
    risk_allocation = calculate_dynamic_risk(signal_strength, risk_reward_ratio, settings)

    # Determine trailing method
    trailing_method = determine_trailing_method(signal_strength, confluence_signals)

    # Calculate confidence score
    confidence = calculate_confidence_score(confluence_signals, risk_reward_ratio, signal_strength)

    # Create trade signal
    trade_signal = TradeSignal(
        signal=signal_direction,
        strength=signal_strength,
        entry_price=entry_price,
        stop_loss=stop_loss,
        take_profit=take_profit,
        buy_score=confluence_signals['buy_score'],
        sell_score=confluence_signals['sell_score'],
        confluence_count=confluence_signals['confluence_count'],
        risk_reward_ratio=risk_reward_ratio,
        confidence=confidence,
        timestamp=datetime.now(),
        timeframe_alignment=confluence_signals['timeframe_alignment'],
        trailing_method=trailing_method,
        risk_allocation=risk_allocation,
        notes=f"Confluence: {confluence_signals['confluence_details']}"
    )

    # Log the signal (lazy initialization)
    try:
        get_metrics_tracker().log_signal(trade_signal)
    except Exception as e:
        logger.warning(f"Could not log signal: {e}")

    return trade_signal