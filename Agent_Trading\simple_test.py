#!/usr/bin/env python3
"""Simple test to check what's working"""

print("🔍 Simple import test...")

# Test 1: Basic imports
try:
    import pandas as pd
    print("✅ pandas works")
except Exception as e:
    print(f"❌ pandas failed: {e}")

# Test 2: TA-Lib
try:
    import talib
    print("✅ talib works")
except Exception as e:
    print(f"❌ talib failed: {e}")

# Test 3: Our package structure
try:
    from crypto_market.engines.data_engine import DatabaseManager
    print("✅ data_engine works")
except Exception as e:
    print(f"❌ data_engine failed: {e}")

print("🎯 Simple test complete!")
