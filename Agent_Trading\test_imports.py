#!/usr/bin/env python3
"""
Test Import Issues
==================

Simple script to test and diagnose import issues step by step.
"""

import sys
from pathlib import Path

print("🔍 Testing imports step by step...")

# Test 1: Basic imports
print("\n1️⃣ Testing basic imports...")
try:
    import pandas as pd
    import numpy as np
    print("✅ pandas, numpy work")
except Exception as e:
    print(f"❌ Basic imports failed: {e}")
    sys.exit(1)

# Test 2: TA-Lib
print("\n2️⃣ Testing TA-Lib...")
try:
    import talib
    print(f"✅ TA-Lib works (version: {talib.__version__})")
except Exception as e:
    print(f"❌ TA-Lib failed: {e}")

# Test 3: VectorBT
print("\n3️⃣ Testing VectorBT...")
try:
    import vectorbt as vbt
    print(f"✅ VectorBT works (version: {vbt.__version__})")
except Exception as e:
    print(f"❌ VectorBT failed: {e}")

# Test 4: Data engine imports
print("\n4️⃣ Testing data engine imports...")
try:
    from crypto_market.engines.data_engine import DatabaseManager
    print("✅ Data engine imports work")
except Exception as e:
    print(f"❌ Data engine imports failed: {e}")

# Test 5: Enhanced signal helpers
print("\n5️⃣ Testing enhanced signal helpers...")
try:
    from crypto_market.engines.Trading_engine.enhanced_signal_helpers import SignalStrength
    print("✅ Enhanced signal helpers work")
except Exception as e:
    print(f"❌ Enhanced signal helpers failed: {e}")

# Test 6: Enhanced indicator (the problematic one)
print("\n6️⃣ Testing enhanced indicator...")
try:
    from crypto_market.engines.Trading_engine.enhanced_indicator import generate_enhanced_signals
    print("✅ Enhanced indicator works")
except Exception as e:
    print(f"❌ Enhanced indicator failed: {e}")
    import traceback
    traceback.print_exc()

# Test 7: Trading engine package
print("\n7️⃣ Testing trading engine package...")
try:
    from crypto_market.engines.Trading_engine import TradingSystem
    print("✅ Trading engine package works")
except Exception as e:
    print(f"❌ Trading engine package failed: {e}")
    import traceback
    traceback.print_exc()

# Test 8: Dashboard imports
print("\n8️⃣ Testing dashboard imports...")
try:
    from dashboard.dashboard_imports import get_trading_components
    components = get_trading_components()
    print(f"✅ Dashboard imports work! Got {len(components)} components")
except Exception as e:
    print(f"❌ Dashboard imports failed: {e}")
    import traceback
    traceback.print_exc()

print("\n🎯 Import testing complete!")
