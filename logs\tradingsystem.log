2025-05-26 17:52:54 - TradingSystem - INFO -    Target: 300-3000 points
2025-05-26 17:52:54 - TradingSystem - INFO -    Timeframes: ['3m', '15m', '1h']
2025-05-26 17:52:55 - TradingSystem - INFO -    Target: 300-3000 points
2025-05-26 17:52:55 - TradingSystem - INFO -    Timeframes: ['3m', '15m', '1h']
2025-05-26 17:52:55 - TradingSystem - INFO -    Quality thresholds: Confidence>70%, Strength>65%
2025-05-26 19:46:16 - TradingSystem - INFO -    Primary: 3m
2025-05-26 19:46:16 - TradingSystem - INFO -    Confirmation: ['15m', '1h']
2025-05-26 20:26:57 - TradingSystem - INFO - [START] Professional logging system initialized: TradingSystem
2025-05-26 20:26:57 - TradingSystem - INFO - [OK] Delta Exchange client initialized
2025-05-26 20:26:57 - TradingSystem - INFO - [OK] Found BTCUSD product: ID 27, type: None
2025-05-26 20:26:57 - TradingSystem - INFO - [OK] Market data feed initialized - BTC Product ID: 27
2025-05-26 20:26:57 - TradingSystem - INFO - [DATA] Fetching data with params: {'resolution': '1h', 'start': 1748165217, 'end': 1748251617, 'symbol': 'BTCUSD'}
2025-05-26 20:26:57 - TradingSystem - INFO - [DATA] API Response keys: ['result', 'success']
2025-05-26 20:26:57 - TradingSystem - INFO - [DATA] Received 24 raw candles
2025-05-26 20:26:57 - TradingSystem - INFO - [DEBUG] Sample candle data: {'close': 109674.5, 'high': 109950, 'low': 109556, 'open': 109646, 'time': 1748250000, 'volume': 218502}
2025-05-26 20:26:57 - TradingSystem - INFO - [DEBUG] Candle length: 6
2025-05-26 20:26:57 - TradingSystem - ERROR - [ERROR] Failed to process candle data: 0
2025-05-26 20:26:57 - TradingSystem - INFO - [OK] Fetched 0 1h candles for BTC
2025-05-26 20:27:43 - TradingSystem - INFO - [START] Professional logging system initialized: TradingSystem
2025-05-26 20:27:43 - TradingSystem - INFO - [OK] Delta Exchange client initialized
2025-05-26 20:27:44 - TradingSystem - INFO - [OK] Found BTCUSD product: ID 27, type: None
2025-05-26 20:27:44 - TradingSystem - INFO - [OK] Market data feed initialized - BTC Product ID: 27
2025-05-26 20:27:44 - TradingSystem - INFO - [DATA] Fetching data with params: {'resolution': '1h', 'start': 1748165264, 'end': 1748251664, 'symbol': 'BTCUSD'}
2025-05-26 20:27:44 - TradingSystem - INFO - [DATA] API Response keys: ['result', 'success']
2025-05-26 20:27:44 - TradingSystem - INFO - [DATA] Received 24 raw candles
2025-05-26 20:27:44 - TradingSystem - INFO - [DEBUG] Sample candle data: {'close': 109674.5, 'high': 109950, 'low': 109556, 'open': 109646, 'time': 1748250000, 'volume': 218502}
2025-05-26 20:27:44 - TradingSystem - INFO - [DEBUG] Candle length: 6
2025-05-26 20:27:44 - TradingSystem - WARNING - [WARNING] Removed 1 invalid candles
2025-05-26 20:27:44 - TradingSystem - INFO - [OK] Fetched 23 1h candles for BTC
2025-05-26 20:30:12 - TradingSystem - INFO - [START] Professional logging system initialized: TradingSystem
2025-05-26 20:30:12 - TradingSystem - INFO - [OK] Delta Exchange client initialized
2025-05-26 20:30:12 - TradingSystem - INFO - [OK] Found BTCUSD product: ID 27, type: None
2025-05-26 20:30:12 - TradingSystem - INFO - [OK] Market data feed initialized - BTC Product ID: 27
2025-05-26 20:30:12 - TradingSystem - INFO -    Primary: 3m
2025-05-26 20:30:12 - TradingSystem - INFO -    Confirmation: ['15m', '1h']
2025-05-26 20:46:56 - TradingSystem - INFO - [START] Professional logging system initialized: TradingSystem
2025-05-26 20:46:56 - TradingSystem - INFO - [OK] Delta Exchange client initialized
2025-05-26 20:46:56 - TradingSystem - INFO - [OK] Found BTCUSD product: ID 27, type: None
2025-05-26 20:46:56 - TradingSystem - INFO - [OK] Market data feed initialized - BTC Product ID: 27
2025-05-26 20:46:56 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-05-26 20:46:56 - TradingSystem - INFO - [OK] Database manager initialized: Agent_Trading\crypto_market\data\crypto_trading.db
2025-05-26 20:46:56 - TradingSystem - INFO - [OK] Data validator initialized
2025-05-26 20:46:56 - TradingSystem - INFO - [OK] Timeframe manager initialized
2025-05-26 20:46:56 - TradingSystem - INFO -    Primary: 3m
2025-05-26 20:46:56 - TradingSystem - INFO -    Confirmation: ['15m', '1h']
2025-05-26 20:46:56 - TradingSystem - INFO - [OK] Historical data manager initialized
2025-05-26 20:46:56 - TradingSystem - INFO - [OK] Delta Exchange client initialized
2025-05-26 20:46:57 - TradingSystem - INFO - [OK] Found BTCUSD product: ID 27, type: None
2025-05-26 20:46:57 - TradingSystem - INFO - [OK] Market data feed initialized - BTC Product ID: 27
2025-05-26 20:46:57 - TradingSystem - INFO - [DATA] Fetching data with params: {'resolution': '1h', 'start': 1748166417, 'end': 1748252817, 'symbol': 'BTCUSD'}
2025-05-26 20:46:57 - TradingSystem - INFO - [DATA] API Response keys: ['result', 'success']
2025-05-26 20:46:57 - TradingSystem - INFO - [DATA] Received 24 raw candles
2025-05-26 20:46:57 - TradingSystem - INFO - [DEBUG] Sample candle data: {'close': 109674.5, 'high': 109950, 'low': 109556, 'open': 109646, 'time': 1748250000, 'volume': 218502}
2025-05-26 20:46:57 - TradingSystem - INFO - [DEBUG] Candle length: 6
2025-05-26 20:46:57 - TradingSystem - WARNING - [WARNING] Removed 1 invalid candles
2025-05-26 20:46:57 - TradingSystem - INFO - [OK] Fetched 23 1h candles for BTC
2025-05-26 20:46:57 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-05-26 20:46:57 - TradingSystem - INFO - [OK] Database manager initialized: Agent_Trading\crypto_market\data\crypto_trading.db
2025-05-26 20:46:57 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-05-26 20:46:57 - TradingSystem - INFO - [OK] Database manager initialized: Agent_Trading\crypto_market\data\crypto_trading.db
2025-05-26 20:46:57 - TradingSystem - INFO - [OK] Delta Exchange client initialized
2025-05-26 20:46:57 - TradingSystem - INFO - [OK] Found BTCUSD product: ID 27, type: None
2025-05-26 20:46:57 - TradingSystem - INFO - [OK] Market data feed initialized - BTC Product ID: 27
2025-05-26 20:46:57 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-05-26 20:46:57 - TradingSystem - INFO - [OK] Database manager initialized: Agent_Trading\crypto_market\data\crypto_trading.db
2025-05-26 20:46:57 - TradingSystem - INFO - [OK] Data validator initialized
2025-05-26 20:46:57 - TradingSystem - INFO - [OK] Timeframe manager initialized
2025-05-26 20:46:57 - TradingSystem - INFO -    Primary: 3m
2025-05-26 20:46:57 - TradingSystem - INFO -    Confirmation: ['15m', '1h']
2025-05-26 20:46:57 - TradingSystem - INFO - [OK] Historical data manager initialized
2025-05-26 20:46:57 - TradingSystem - INFO - [OK] Retrieved 0 records for BTCUSD 1h
2025-05-26 20:47:56 - TradingSystem - INFO - [START] Professional logging system initialized: TradingSystem
2025-05-26 20:47:56 - TradingSystem - INFO - [OK] Delta Exchange client initialized
2025-05-26 20:47:56 - TradingSystem - INFO - [OK] Found BTCUSD product: ID 27, type: None
2025-05-26 20:47:56 - TradingSystem - INFO - [OK] Market data feed initialized - BTC Product ID: 27
2025-05-26 20:47:56 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-05-26 20:47:56 - TradingSystem - INFO - [OK] Database manager initialized: Agent_Trading\crypto_market\data\crypto_trading.db
2025-05-26 20:47:56 - TradingSystem - INFO - [OK] Data validator initialized
2025-05-26 20:47:56 - TradingSystem - INFO - [OK] Timeframe manager initialized
2025-05-26 20:47:56 - TradingSystem - INFO -    Primary: 3m
2025-05-26 20:47:56 - TradingSystem - INFO -    Confirmation: ['15m', '1h']
2025-05-26 20:47:56 - TradingSystem - INFO - [OK] Historical data manager initialized
2025-05-26 20:47:56 - TradingSystem - INFO - [OK] Delta Exchange client initialized
2025-05-26 20:47:56 - TradingSystem - INFO - [OK] Found BTCUSD product: ID 27, type: None
2025-05-26 20:47:56 - TradingSystem - INFO - [OK] Market data feed initialized - BTC Product ID: 27
2025-05-26 20:47:56 - TradingSystem - INFO - [DATA] Fetching data with params: {'resolution': '1h', 'start': 1748166476, 'end': 1748252876, 'symbol': 'BTCUSD'}
2025-05-26 20:47:57 - TradingSystem - INFO - [DATA] API Response keys: ['result', 'success']
2025-05-26 20:47:57 - TradingSystem - INFO - [DATA] Received 24 raw candles
2025-05-26 20:47:57 - TradingSystem - INFO - [DEBUG] Sample candle data: {'close': 109674.5, 'high': 109950, 'low': 109556, 'open': 109646, 'time': 1748250000, 'volume': 218502}
2025-05-26 20:47:57 - TradingSystem - INFO - [DEBUG] Candle length: 6
2025-05-26 20:47:57 - TradingSystem - WARNING - [WARNING] Removed 1 invalid candles
2025-05-26 20:47:57 - TradingSystem - INFO - [OK] Fetched 23 1h candles for BTC
2025-05-26 20:47:57 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-05-26 20:47:57 - TradingSystem - INFO - [OK] Database manager initialized: Agent_Trading\crypto_market\data\crypto_trading.db
2025-05-26 20:47:57 - TradingSystem - INFO - [OK] Stored 23 market data records for BTCUSD
2025-05-26 20:47:57 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-05-26 20:47:57 - TradingSystem - INFO - [OK] Database manager initialized: Agent_Trading\crypto_market\data\crypto_trading.db
2025-05-26 20:47:57 - TradingSystem - INFO - [OK] Delta Exchange client initialized
2025-05-26 20:47:57 - TradingSystem - INFO - [OK] Found BTCUSD product: ID 27, type: None
2025-05-26 20:47:57 - TradingSystem - INFO - [OK] Market data feed initialized - BTC Product ID: 27
2025-05-26 20:47:57 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-05-26 20:47:57 - TradingSystem - INFO - [OK] Database manager initialized: Agent_Trading\crypto_market\data\crypto_trading.db
2025-05-26 20:47:57 - TradingSystem - INFO - [OK] Data validator initialized
2025-05-26 20:47:57 - TradingSystem - INFO - [OK] Timeframe manager initialized
2025-05-26 20:47:57 - TradingSystem - INFO -    Primary: 3m
2025-05-26 20:47:57 - TradingSystem - INFO -    Confirmation: ['15m', '1h']
2025-05-26 20:47:57 - TradingSystem - INFO - [OK] Historical data manager initialized
2025-05-26 20:47:57 - TradingSystem - INFO - [OK] Retrieved 23 records for BTCUSD 1h
2025-05-26 20:53:13 - TradingSystem - INFO - [START] Professional logging system initialized: TradingSystem
2025-05-26 20:53:13 - TradingSystem - INFO - [OK] Delta Exchange client initialized
2025-05-26 20:53:14 - TradingSystem - INFO - [OK] Found BTCUSD product: ID 27, type: None
2025-05-26 20:53:14 - TradingSystem - INFO - [OK] Market data feed initialized - BTC Product ID: 27
2025-05-26 20:53:14 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-05-26 20:53:14 - TradingSystem - INFO - [OK] Database manager initialized: Agent_Trading\crypto_market\data\crypto_trading.db
2025-05-26 20:53:14 - TradingSystem - INFO - [OK] Data validator initialized
2025-05-26 20:53:14 - TradingSystem - INFO - [OK] Timeframe manager initialized
2025-05-26 20:53:14 - TradingSystem - INFO -    Primary: 3m
2025-05-26 20:53:14 - TradingSystem - INFO -    Confirmation: ['15m', '1h']
2025-05-26 20:53:14 - TradingSystem - INFO - [OK] Historical data manager initialized
2025-05-26 20:53:14 - TradingSystem - INFO - [OK] Delta Exchange client initialized
2025-05-26 20:53:14 - TradingSystem - INFO - [OK] Found BTCUSD product: ID 27, type: None
2025-05-26 20:53:14 - TradingSystem - INFO - [OK] Market data feed initialized - BTC Product ID: 27
2025-05-26 20:53:14 - TradingSystem - INFO - [DATA] Fetching data with params: {'resolution': '1h', 'start': 1748166794, 'end': 1748253194, 'symbol': 'BTCUSD'}
2025-05-26 20:53:14 - TradingSystem - INFO - [DATA] API Response keys: ['result', 'success']
2025-05-26 20:53:14 - TradingSystem - INFO - [DATA] Received 24 raw candles
2025-05-26 20:53:14 - TradingSystem - INFO - [DEBUG] Sample candle data: {'close': 109674.5, 'high': 109950, 'low': 109556, 'open': 109646, 'time': 1748250000, 'volume': 218502}
2025-05-26 20:53:14 - TradingSystem - INFO - [DEBUG] Candle length: 6
2025-05-26 20:53:14 - TradingSystem - WARNING - [WARNING] Removed 1 invalid candles
2025-05-26 20:53:14 - TradingSystem - INFO - [OK] Fetched 23 1h candles for BTC
2025-05-26 20:53:14 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-05-26 20:53:14 - TradingSystem - INFO - [OK] Database manager initialized: Agent_Trading\crypto_market\data\crypto_trading.db
2025-05-26 20:53:14 - TradingSystem - INFO - [OK] Stored 23 market data records for BTCUSD
2025-05-26 20:53:14 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-05-26 20:53:14 - TradingSystem - INFO - [OK] Database manager initialized: Agent_Trading\crypto_market\data\crypto_trading.db
2025-05-26 20:53:14 - TradingSystem - INFO - [OK] Delta Exchange client initialized
2025-05-26 20:53:15 - TradingSystem - INFO - [OK] Found BTCUSD product: ID 27, type: None
2025-05-26 20:53:15 - TradingSystem - INFO - [OK] Market data feed initialized - BTC Product ID: 27
2025-05-26 20:53:15 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-05-26 20:53:15 - TradingSystem - INFO - [OK] Database manager initialized: Agent_Trading\crypto_market\data\crypto_trading.db
2025-05-26 20:53:15 - TradingSystem - INFO - [OK] Data validator initialized
2025-05-26 20:53:15 - TradingSystem - INFO - [OK] Timeframe manager initialized
2025-05-26 20:53:15 - TradingSystem - INFO -    Primary: 3m
2025-05-26 20:53:15 - TradingSystem - INFO -    Confirmation: ['15m', '1h']
2025-05-26 20:53:15 - TradingSystem - INFO - [OK] Historical data manager initialized
2025-05-26 20:53:15 - TradingSystem - INFO - [OK] Retrieved 23 records for BTCUSD 1h
2025-05-26 20:54:05 - TradingSystem - INFO - [START] Professional logging system initialized: TradingSystem
2025-05-26 20:54:05 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-05-26 20:54:05 - TradingSystem - INFO - [OK] Database manager initialized: Agent_Trading\crypto_market\data\crypto_trading.db
2025-05-26 20:54:05 - TradingSystem - INFO - [OK] Delta Exchange client initialized
2025-05-26 20:54:05 - TradingSystem - INFO - [OK] Found BTCUSD product: ID 27, type: None
2025-05-26 20:54:05 - TradingSystem - INFO - [OK] Market data feed initialized - BTC Product ID: 27
2025-05-26 20:54:05 - TradingSystem - INFO - [OK] Delta Exchange client initialized
2025-05-26 20:54:05 - TradingSystem - INFO - [OK] Found BTCUSD product: ID 27, type: None
2025-05-26 20:54:05 - TradingSystem - INFO - [OK] Market data feed initialized - BTC Product ID: 27
2025-05-26 20:54:05 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-05-26 20:54:05 - TradingSystem - INFO - [OK] Database manager initialized: Agent_Trading\crypto_market\data\crypto_trading.db
2025-05-26 20:54:05 - TradingSystem - INFO - [OK] Data validator initialized
2025-05-26 20:54:05 - TradingSystem - INFO - [OK] Timeframe manager initialized
2025-05-26 20:54:05 - TradingSystem - INFO -    Primary: 3m
2025-05-26 20:54:05 - TradingSystem - INFO -    Confirmation: ['15m', '1h']
2025-05-26 20:54:05 - TradingSystem - INFO - [OK] Historical data manager initialized
2025-05-26 20:54:05 - TradingSystem - INFO - [OK] Retrieved 23 records for BTCUSD 1h
2025-05-26 20:54:05 - TradingSystem - INFO - Test info message
2025-05-26 20:54:05 - TradingSystem - WARNING - Test warning message
2025-05-26 20:54:05 - TradingSystem - ERROR - Test error message
2025-05-26 20:54:05 - TradingSystem - INFO - [OK] This should be cleaned
2025-05-26 20:54:05 - TradingSystem - INFO - [ERROR] This should also be cleaned
2025-05-26 20:54:05 - TradingSystem - INFO - [WARNING] Warning with emoji
2025-05-26 20:54:05 - TradingSystem - INFO - [START] Start message
2025-05-26 20:54:05 - TradingSystem - INFO - [DATA] Data message
2025-05-27 01:06:20 - TradingSystem - INFO - [START] Professional logging system initialized: TradingSystem
2025-05-27 01:06:20 - TradingSystem - INFO - [OK] Delta Exchange client initialized
2025-05-27 01:06:20 - TradingSystem - INFO - [OK] Found BTCUSD product: ID 27, type: None
2025-05-27 01:06:21 - TradingSystem - INFO - [OK] Market data feed initialized - BTC Product ID: 27
2025-05-27 01:06:21 - TradingSystem - INFO - [CHUNKED_DEBUG] Timeframe: 3m, Days requested: 20, Max per chunk: 7
2025-05-27 01:06:21 - TradingSystem - INFO - [CHUNKED_DEBUG] Using chunked requests for 20 days
2025-05-27 01:06:21 - TradingSystem - INFO - [CHUNKED] Fetching 20 days of 3m data in 3 chunks
2025-05-27 01:06:21 - TradingSystem - INFO - [CHUNKED] Max days per chunk: 7
2025-05-27 01:06:21 - TradingSystem - INFO - [CHUNKED] Fetching chunk 1/3: 7 days ending at 2025-05-26 19:36:21.001342
2025-05-27 01:06:21 - TradingSystem - INFO - [DATA] Fetching data with params: {'resolution': '3m', 'start': 1747663581, 'end': 1748268381, 'symbol': 'BTCUSD'}
2025-05-27 01:06:21 - TradingSystem - INFO - [DATA] API Response keys: ['result', 'success']
2025-05-27 01:06:21 - TradingSystem - INFO - [DATA] Received 3360 raw candles
2025-05-27 01:06:21 - TradingSystem - INFO - [DEBUG] Sample candle data: {'close': 109511.5, 'high': 109613, 'low': 109501.5, 'open': 109591, 'time': 1748268360, 'volume': 11848}
2025-05-27 01:06:21 - TradingSystem - INFO - [DEBUG] Candle length: 6
2025-05-27 01:06:22 - TradingSystem - WARNING - [WARNING] Removed 1 invalid candles
2025-05-27 01:06:22 - TradingSystem - INFO - [OK] Fetched 3359 3m candles for BTC
2025-05-27 01:06:22 - TradingSystem - INFO - [CHUNKED] Chunk 1 successful: 3359 candles
2025-05-27 01:06:22 - TradingSystem - INFO - [CHUNKED] Fetching chunk 2/3: 7 days ending at 2025-05-19 19:36:21.001342
2025-05-27 01:06:22 - TradingSystem - INFO - [DATA] Fetching data with params: {'resolution': '3m', 'start': 1747058781, 'end': 1747663581, 'symbol': 'BTCUSD'}
2025-05-27 01:06:23 - TradingSystem - INFO - [DATA] API Response keys: ['result', 'success']
2025-05-27 01:06:23 - TradingSystem - INFO - [DATA] Received 3360 raw candles
2025-05-27 01:06:23 - TradingSystem - INFO - [DEBUG] Sample candle data: {'close': 103453, 'high': 103453, 'low': 103142.5, 'open': 103189.5, 'time': 1747663560, 'volume': 31256}
2025-05-27 01:06:23 - TradingSystem - INFO - [DEBUG] Candle length: 6
2025-05-27 01:06:23 - TradingSystem - WARNING - [WARNING] Removed 1 invalid candles
2025-05-27 01:06:23 - TradingSystem - INFO - [OK] Fetched 3359 3m candles for BTC
2025-05-27 01:06:23 - TradingSystem - INFO - [CHUNKED] Chunk 2 successful: 3359 candles
2025-05-27 01:06:24 - TradingSystem - INFO - [CHUNKED] Fetching chunk 3/3: 6 days ending at 2025-05-12 19:36:21.001342
2025-05-27 01:06:24 - TradingSystem - INFO - [DATA] Fetching data with params: {'resolution': '3m', 'start': 1746540381, 'end': 1747058781, 'symbol': 'BTCUSD'}
2025-05-27 01:06:24 - TradingSystem - INFO - [DATA] API Response keys: ['result', 'success']
2025-05-27 01:06:24 - TradingSystem - INFO - [DATA] Received 2880 raw candles
2025-05-27 01:06:24 - TradingSystem - INFO - [DEBUG] Sample candle data: {'close': 104303.5, 'high': 104303.5, 'low': 104151.5, 'open': 104274.5, 'time': 1747058760, 'volume': 6444}
2025-05-27 01:06:24 - TradingSystem - INFO - [DEBUG] Candle length: 6
2025-05-27 01:06:25 - TradingSystem - WARNING - [WARNING] Removed 1 invalid candles
2025-05-27 01:06:25 - TradingSystem - INFO - [OK] Fetched 2879 3m candles for BTC
2025-05-27 01:06:25 - TradingSystem - INFO - [CHUNKED] Chunk 3 successful: 2879 candles
2025-05-27 01:06:25 - TradingSystem - INFO - [CHUNKED] Combined 3 chunks into 9597 total candles
2025-05-27 01:06:25 - TradingSystem - INFO - [CHUNKED] Date range: 2025-05-06 14:12:00 to 2025-05-26 14:06:00
2025-05-27 02:20:18 - TradingSystem - INFO - [START] Professional logging system initialized: TradingSystem
2025-05-27 02:20:18 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-05-27 02:20:18 - TradingSystem - INFO - [OK] Database manager initialized: Agent_Trading\crypto_market\data\crypto_trading.db
2025-05-27 02:21:07 - TradingSystem - INFO - [START] Professional logging system initialized: TradingSystem
2025-05-27 02:21:07 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-05-27 02:21:07 - TradingSystem - INFO - [OK] Database manager initialized: Agent_Trading\crypto_market\data\crypto_trading.db
2025-05-27 02:21:24 - TradingSystem - INFO - [OK] Cleared 286003 records from market_data
2025-05-27 02:21:24 - TradingSystem - INFO - [OK] Cleared 0 records from indicators
2025-05-27 02:21:24 - TradingSystem - INFO - [OK] Cleared 0 records from signals
2025-05-27 02:21:24 - TradingSystem - INFO - [OK] Cleared 0 records from trades
2025-05-27 02:21:24 - TradingSystem - INFO - [OK] Cleared 0 records from performance_metrics
2025-05-27 02:21:24 - TradingSystem - INFO - [OK] All database tables cleared successfully
2025-05-27 02:21:53 - TradingSystem - INFO - [START] Professional logging system initialized: TradingSystem
2025-05-27 02:21:53 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-05-27 02:21:53 - TradingSystem - INFO - [OK] Database manager initialized: Agent_Trading\crypto_market\data\crypto_trading.db
2025-05-27 11:54:10 - TradingSystem - INFO - [START] Professional logging system initialized: TradingSystem
2025-05-27 11:54:10 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-05-27 11:54:10 - TradingSystem - INFO - [OK] Database manager initialized: Agent_Trading\crypto_market\data\crypto_trading.db
2025-05-27 12:27:15 - TradingSystem - INFO - [START] Professional logging system initialized: TradingSystem
2025-05-27 12:27:15 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-05-27 12:27:15 - TradingSystem - INFO - [OK] Database manager initialized: Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-05-27 12:27:15 - TradingSystem - INFO - [OK] Delta Exchange client initialized
2025-05-27 12:27:15 - TradingSystem - INFO - [OK] Found BTCUSD product: ID 27, type: None
2025-05-27 12:27:15 - TradingSystem - INFO - [OK] Market data feed initialized - BTC Product ID: 27
2025-05-27 12:27:15 - TradingSystem - INFO - [CHUNKED_DEBUG] Timeframe: 3m, Days requested: 1, Max per chunk: 7
2025-05-27 12:27:15 - TradingSystem - INFO - [CHUNKED_DEBUG] Using single request for 1 days
2025-05-27 12:27:15 - TradingSystem - INFO - [DATA] Fetching data with params: {'resolution': '3m', 'start': 1748222835, 'end': 1748309235, 'symbol': 'BTCUSD'}
2025-05-27 12:27:16 - TradingSystem - INFO - [DATA] API Response keys: ['result', 'success']
2025-05-27 12:27:16 - TradingSystem - INFO - [DATA] Received 480 raw candles
2025-05-27 12:27:16 - TradingSystem - INFO - [DEBUG] Sample candle data: {'close': 108331, 'high': 108358.5, 'low': 108000.5, 'open': 108000.5, 'time': 1748309220, 'volume': 28156}
2025-05-27 12:27:16 - TradingSystem - INFO - [DEBUG] Candle length: 6
2025-05-27 12:27:16 - TradingSystem - WARNING - [WARNING] Removed 1 invalid candles
2025-05-27 12:27:16 - TradingSystem - INFO - [OK] Fetched 479 3m candles for BTC
2025-05-27 12:27:16 - TradingSystem - INFO - [DEBUG] Processing 479 rows for storage
2025-05-27 12:27:16 - TradingSystem - INFO - [DEBUG] DataFrame columns: ['open', 'high', 'low', 'close', 'volume', 'timeframe']
2025-05-27 12:27:16 - TradingSystem - INFO - [DEBUG] DataFrame index type: <class 'pandas._libs.tslibs.timestamps.Timestamp'>
2025-05-27 12:27:16 - TradingSystem - INFO - [DEBUG] Record 0: timestamp=2025-05-26 01:33:00, open=109217.5, timeframe=3m
2025-05-27 12:27:16 - TradingSystem - INFO - [DEBUG] Record 1: timestamp=2025-05-26 01:36:00, open=109317.0, timeframe=3m
2025-05-27 12:27:16 - TradingSystem - INFO - [DEBUG] Record 2: timestamp=2025-05-26 01:39:00, open=109481.5, timeframe=3m
2025-05-27 12:27:16 - TradingSystem - INFO - [DEBUG] Prepared 479 records for insertion
2025-05-27 12:27:16 - TradingSystem - INFO - [OK] Stored 479 market data records for BTCUSD
2025-05-27 12:27:17 - TradingSystem - INFO - [OK] Retrieved 10 records for BTCUSD 3m
2025-05-27 12:28:39 - TradingSystem - INFO - [START] Professional logging system initialized: TradingSystem
2025-05-27 12:28:39 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-05-27 12:28:39 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-05-27 12:28:39 - TradingSystem - INFO - [OK] Delta Exchange client initialized
2025-05-27 12:28:39 - TradingSystem - INFO - [OK] Found BTCUSD product: ID 27, type: None
2025-05-27 12:28:39 - TradingSystem - INFO - [OK] Market data feed initialized - BTC Product ID: 27
2025-05-27 12:28:39 - TradingSystem - INFO - [CHUNKED_DEBUG] Timeframe: 3m, Days requested: 1, Max per chunk: 7
2025-05-27 12:28:39 - TradingSystem - INFO - [CHUNKED_DEBUG] Using single request for 1 days
2025-05-27 12:28:39 - TradingSystem - INFO - [DATA] Fetching data with params: {'resolution': '3m', 'start': 1748222919, 'end': 1748309319, 'symbol': 'BTCUSD'}
2025-05-27 12:28:40 - TradingSystem - INFO - [DATA] API Response keys: ['result', 'success']
2025-05-27 12:28:40 - TradingSystem - INFO - [DATA] Received 480 raw candles
2025-05-27 12:28:40 - TradingSystem - INFO - [DEBUG] Sample candle data: {'close': 108331, 'high': 108358.5, 'low': 108000.5, 'open': 108000.5, 'time': 1748309220, 'volume': 28156}
2025-05-27 12:28:40 - TradingSystem - INFO - [DEBUG] Candle length: 6
2025-05-27 12:28:40 - TradingSystem - WARNING - [WARNING] Removed 1 invalid candles
2025-05-27 12:28:40 - TradingSystem - INFO - [OK] Fetched 479 3m candles for BTC
2025-05-27 12:28:40 - TradingSystem - INFO - [DEBUG] Processing 479 rows for storage
2025-05-27 12:28:40 - TradingSystem - INFO - [DEBUG] DataFrame columns: ['open', 'high', 'low', 'close', 'volume', 'timeframe']
2025-05-27 12:28:40 - TradingSystem - INFO - [DEBUG] DataFrame index type: <class 'pandas._libs.tslibs.timestamps.Timestamp'>
2025-05-27 12:28:40 - TradingSystem - INFO - [DEBUG] Record 0: timestamp=2025-05-26 01:33:00, open=109217.5, timeframe=3m
2025-05-27 12:28:40 - TradingSystem - INFO - [DEBUG] Record 1: timestamp=2025-05-26 01:36:00, open=109317.0, timeframe=3m
2025-05-27 12:28:40 - TradingSystem - INFO - [DEBUG] Record 2: timestamp=2025-05-26 01:39:00, open=109481.5, timeframe=3m
2025-05-27 12:28:41 - TradingSystem - INFO - [DEBUG] Prepared 479 records for insertion
2025-05-27 12:28:41 - TradingSystem - INFO - [OK] Stored 479 market data records for BTCUSD
2025-05-27 12:28:41 - TradingSystem - INFO - [OK] Retrieved 10 records for BTCUSD 3m
2025-05-27 12:34:36 - TradingSystem - INFO - [START] Professional logging system initialized: TradingSystem
2025-05-27 12:34:37 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-05-27 12:34:37 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-05-27 12:34:37 - TradingSystem - INFO - [OK] Delta Exchange client initialized
2025-05-27 12:34:37 - TradingSystem - INFO - [OK] Found BTCUSD product: ID 27, type: None
2025-05-27 12:34:37 - TradingSystem - INFO - [OK] Market data feed initialized - BTC Product ID: 27
2025-05-27 12:34:37 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-05-27 12:34:37 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-05-27 12:34:37 - TradingSystem - INFO - [OK] Simple data fetcher initialized
2025-05-27 12:34:37 - TradingSystem - INFO - [START] Simple data fetch: 1 days, timeframes: ['3m']
2025-05-27 12:34:37 - TradingSystem - INFO - [FETCH] Processing 3m timeframe...
2025-05-27 12:34:37 - TradingSystem - INFO - [CHUNKED_DEBUG] Timeframe: 3m, Days requested: 1, Max per chunk: 7
2025-05-27 12:34:37 - TradingSystem - INFO - [CHUNKED_DEBUG] Using single request for 1 days
2025-05-27 12:34:37 - TradingSystem - INFO - [DATA] Fetching data with params: {'resolution': '3m', 'start': 1748223277, 'end': 1748309677, 'symbol': 'BTCUSD'}
2025-05-27 12:34:37 - TradingSystem - INFO - [DATA] API Response keys: ['result', 'success']
2025-05-27 12:34:37 - TradingSystem - INFO - [DATA] Received 480 raw candles
2025-05-27 12:34:37 - TradingSystem - INFO - [DEBUG] Sample candle data: {'close': 108590, 'high': 108611, 'low': 108472.5, 'open': 108505, 'time': 1748309580, 'volume': 23846}
2025-05-27 12:34:37 - TradingSystem - INFO - [DEBUG] Candle length: 6
2025-05-27 12:34:37 - TradingSystem - WARNING - [WARNING] Removed 1 invalid candles
2025-05-27 12:34:37 - TradingSystem - INFO - [OK] Fetched 479 3m candles for BTC
2025-05-27 12:34:37 - TradingSystem - INFO - [DEBUG] Processing 479 rows for storage
2025-05-27 12:34:37 - TradingSystem - INFO - [DEBUG] DataFrame columns: ['open', 'high', 'low', 'close', 'volume', 'timeframe']
2025-05-27 12:34:37 - TradingSystem - INFO - [DEBUG] DataFrame index type: <class 'pandas._libs.tslibs.timestamps.Timestamp'>
2025-05-27 12:34:37 - TradingSystem - INFO - [DEBUG] Record 0: timestamp=2025-05-26 01:39:00, open=109481.5, timeframe=3m
2025-05-27 12:34:37 - TradingSystem - INFO - [DEBUG] Record 1: timestamp=2025-05-26 01:42:00, open=109600.5, timeframe=3m
2025-05-27 12:34:37 - TradingSystem - INFO - [DEBUG] Record 2: timestamp=2025-05-26 01:45:00, open=109626.5, timeframe=3m
2025-05-27 12:34:37 - TradingSystem - INFO - [DEBUG] Prepared 479 records for insertion
2025-05-27 12:34:37 - TradingSystem - INFO - [OK] Stored 479 market data records for BTCUSD
2025-05-27 12:34:37 - TradingSystem - INFO - [OK] Stored 479 3m records
2025-05-27 12:34:38 - TradingSystem - INFO - [SUCCESS] Fetched and stored 479 total records across 1 timeframes
2025-05-27 12:34:38 - TradingSystem - INFO - [START] Custom range fetch: 2025-05-24 to 2025-05-27 (4 days)
2025-05-27 12:34:38 - TradingSystem - INFO - [FETCH] Processing 15m timeframe...
2025-05-27 12:34:38 - TradingSystem - INFO - [CHUNKED_DEBUG] Timeframe: 15m, Days requested: 4, Max per chunk: 36
2025-05-27 12:34:38 - TradingSystem - INFO - [CHUNKED_DEBUG] Using single request for 4 days
2025-05-27 12:34:38 - TradingSystem - INFO - [DATA] Fetching data with params: {'resolution': '15m', 'start': 1747983878, 'end': 1748329478, 'symbol': 'BTCUSD'}
2025-05-27 12:34:38 - TradingSystem - INFO - [DATA] API Response keys: ['result', 'success']
2025-05-27 12:34:38 - TradingSystem - INFO - [DATA] Received 384 raw candles
2025-05-27 12:34:38 - TradingSystem - INFO - [DEBUG] Sample candle data: {'close': 108911, 'high': 108911.5, 'low': 108791, 'open': 108791, 'time': 1748329200, 'volume': 20487}
2025-05-27 12:34:38 - TradingSystem - INFO - [DEBUG] Candle length: 6
2025-05-27 12:34:38 - TradingSystem - WARNING - [WARNING] Removed 1 invalid candles
2025-05-27 12:34:38 - TradingSystem - INFO - [OK] Fetched 383 15m candles for BTC
2025-05-27 12:34:38 - TradingSystem - INFO - [DEBUG] Processing 266 rows for storage
2025-05-27 12:34:38 - TradingSystem - INFO - [DEBUG] DataFrame columns: ['open', 'high', 'low', 'close', 'volume', 'timeframe']
2025-05-27 12:34:38 - TradingSystem - INFO - [DEBUG] DataFrame index type: <class 'pandas._libs.tslibs.timestamps.Timestamp'>
2025-05-27 12:34:38 - TradingSystem - INFO - [DEBUG] Record 0: timestamp=2025-05-24 12:45:00, open=108793.5, timeframe=15m
2025-05-27 12:34:38 - TradingSystem - INFO - [DEBUG] Record 1: timestamp=2025-05-24 13:00:00, open=108751.5, timeframe=15m
2025-05-27 12:34:38 - TradingSystem - INFO - [DEBUG] Record 2: timestamp=2025-05-24 13:15:00, open=108676.0, timeframe=15m
2025-05-27 12:34:38 - TradingSystem - INFO - [DEBUG] Prepared 266 records for insertion
2025-05-27 12:34:38 - TradingSystem - INFO - [OK] Stored 266 market data records for BTCUSD
2025-05-27 12:34:38 - TradingSystem - INFO - [OK] Stored 266 15m records
2025-05-27 12:34:39 - TradingSystem - INFO - [SUCCESS] Custom range fetch completed: 266 total records
2025-05-27 12:34:39 - TradingSystem - INFO - [OK] Retrieved 5 records for BTCUSD 3m
2025-05-27 12:34:39 - TradingSystem - INFO - [OK] Retrieved 5 records for BTCUSD 15m
2025-05-27 12:41:32 - TradingSystem - INFO - [START] Professional logging system initialized: TradingSystem
2025-05-27 12:41:32 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-05-27 12:41:32 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-05-27 12:41:32 - TradingSystem - INFO - [OK] Delta Exchange client initialized
2025-05-27 12:41:32 - TradingSystem - INFO - [OK] Found BTCUSD product: ID 27, type: None
2025-05-27 12:41:32 - TradingSystem - INFO - [OK] Market data feed initialized - BTC Product ID: 27
2025-05-27 12:41:32 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-05-27 12:41:32 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-05-27 12:41:32 - TradingSystem - INFO - [OK] Simple data fetcher initialized
2025-05-27 12:41:32 - TradingSystem - INFO - [OK] Delta Exchange client initialized
2025-05-27 12:41:32 - TradingSystem - INFO - [OK] Found BTCUSD product: ID 27, type: None
2025-05-27 12:41:32 - TradingSystem - INFO - [OK] Market data feed initialized - BTC Product ID: 27
2025-05-27 12:41:32 - TradingSystem - INFO - [CHUNKED_DEBUG] Timeframe: 1h, Days requested: 7, Max per chunk: 145
2025-05-27 12:41:32 - TradingSystem - INFO - [CHUNKED_DEBUG] Using single request for 7 days
2025-05-27 12:41:32 - TradingSystem - INFO - [DATA] Fetching data with params: {'resolution': '1h', 'start': 1747705292, 'end': 1748310092, 'symbol': 'BTCUSD'}
2025-05-27 12:41:32 - TradingSystem - INFO - [DATA] API Response keys: ['result', 'success']
2025-05-27 12:41:32 - TradingSystem - INFO - [DATA] Received 168 raw candles
2025-05-27 12:41:32 - TradingSystem - INFO - [DEBUG] Sample candle data: {'close': 108365.5, 'high': 109116, 'low': 107460.5, 'open': 109115, 'time': 1748307600, 'volume': 476355}
2025-05-27 12:41:32 - TradingSystem - INFO - [DEBUG] Candle length: 6
2025-05-27 12:41:32 - TradingSystem - WARNING - [WARNING] Removed 1 invalid candles
2025-05-27 12:41:32 - TradingSystem - INFO - [OK] Fetched 167 1h candles for BTC
2025-05-27 12:41:32 - TradingSystem - INFO - [DEBUG] Processing 167 rows for storage
2025-05-27 12:41:32 - TradingSystem - INFO - [DEBUG] DataFrame columns: ['open', 'high', 'low', 'close', 'volume', 'timeframe']
2025-05-27 12:41:32 - TradingSystem - INFO - [DEBUG] DataFrame index type: <class 'pandas._libs.tslibs.timestamps.Timestamp'>
2025-05-27 12:41:32 - TradingSystem - INFO - [DEBUG] Record 0: timestamp=2025-05-20 03:00:00, open=106070.0, timeframe=1h
2025-05-27 12:41:32 - TradingSystem - INFO - [DEBUG] Record 1: timestamp=2025-05-20 04:00:00, open=105731.0, timeframe=1h
2025-05-27 12:41:32 - TradingSystem - INFO - [DEBUG] Record 2: timestamp=2025-05-20 05:00:00, open=106192.5, timeframe=1h
2025-05-27 12:41:32 - TradingSystem - INFO - [DEBUG] Prepared 167 records for insertion
2025-05-27 12:41:32 - TradingSystem - INFO - [OK] Stored 167 market data records for BTCUSD
2025-05-27 12:41:32 - TradingSystem - INFO - [START] Simple data fetch: 7 days, timeframes: ['1h']
2025-05-27 12:41:32 - TradingSystem - INFO - [FETCH] Processing 1h timeframe...
2025-05-27 12:41:32 - TradingSystem - INFO - [CHUNKED_DEBUG] Timeframe: 1h, Days requested: 7, Max per chunk: 145
2025-05-27 12:41:32 - TradingSystem - INFO - [CHUNKED_DEBUG] Using single request for 7 days
2025-05-27 12:41:32 - TradingSystem - INFO - [DATA] Fetching data with params: {'resolution': '1h', 'start': 1747705292, 'end': 1748310092, 'symbol': 'BTCUSD'}
2025-05-27 12:41:33 - TradingSystem - INFO - [DATA] API Response keys: ['result', 'success']
2025-05-27 12:41:33 - TradingSystem - INFO - [DATA] Received 168 raw candles
2025-05-27 12:41:33 - TradingSystem - INFO - [DEBUG] Sample candle data: {'close': 108365.5, 'high': 109116, 'low': 107460.5, 'open': 109115, 'time': 1748307600, 'volume': 476355}
2025-05-27 12:41:33 - TradingSystem - INFO - [DEBUG] Candle length: 6
2025-05-27 12:41:33 - TradingSystem - WARNING - [WARNING] Removed 1 invalid candles
2025-05-27 12:41:33 - TradingSystem - INFO - [OK] Fetched 167 1h candles for BTC
2025-05-27 12:41:33 - TradingSystem - INFO - [DEBUG] Processing 167 rows for storage
2025-05-27 12:41:33 - TradingSystem - INFO - [DEBUG] DataFrame columns: ['open', 'high', 'low', 'close', 'volume', 'timeframe']
2025-05-27 12:41:33 - TradingSystem - INFO - [DEBUG] DataFrame index type: <class 'pandas._libs.tslibs.timestamps.Timestamp'>
2025-05-27 12:41:33 - TradingSystem - INFO - [DEBUG] Record 0: timestamp=2025-05-20 03:00:00, open=106070.0, timeframe=1h
2025-05-27 12:41:33 - TradingSystem - INFO - [DEBUG] Record 1: timestamp=2025-05-20 04:00:00, open=105731.0, timeframe=1h
2025-05-27 12:41:33 - TradingSystem - INFO - [DEBUG] Record 2: timestamp=2025-05-20 05:00:00, open=106192.5, timeframe=1h
2025-05-27 12:41:33 - TradingSystem - INFO - [DEBUG] Prepared 167 records for insertion
2025-05-27 12:41:33 - TradingSystem - INFO - [OK] Stored 167 market data records for BTCUSD
2025-05-27 12:41:33 - TradingSystem - INFO - [OK] Stored 167 1h records
2025-05-27 12:41:33 - TradingSystem - INFO - [SUCCESS] Fetched and stored 167 total records across 1 timeframes
2025-05-27 12:41:33 - TradingSystem - INFO - [OK] Retrieved 5 records for BTCUSD 1h
2025-05-27 12:41:33 - TradingSystem - INFO - [START] Simple data fetch: 3 days, timeframes: ['3m', '15m', '1h']
2025-05-27 12:41:33 - TradingSystem - INFO - [FETCH] Processing 3m timeframe...
2025-05-27 12:41:33 - TradingSystem - INFO - [CHUNKED_DEBUG] Timeframe: 3m, Days requested: 3, Max per chunk: 7
2025-05-27 12:41:33 - TradingSystem - INFO - [CHUNKED_DEBUG] Using single request for 3 days
2025-05-27 12:41:33 - TradingSystem - INFO - [DATA] Fetching data with params: {'resolution': '3m', 'start': 1748050893, 'end': 1748310093, 'symbol': 'BTCUSD'}
2025-05-27 12:41:34 - TradingSystem - INFO - [DATA] API Response keys: ['result', 'success']
2025-05-27 12:41:34 - TradingSystem - INFO - [DATA] Received 1440 raw candles
2025-05-27 12:41:34 - TradingSystem - INFO - [DEBUG] Sample candle data: {'close': 108366.5, 'high': 108478.5, 'low': 108365.5, 'open': 108382, 'time': 1748309940, 'volume': 7783}
2025-05-27 12:41:34 - TradingSystem - INFO - [DEBUG] Candle length: 6
2025-05-27 12:41:34 - TradingSystem - WARNING - [WARNING] Removed 1 invalid candles
2025-05-27 12:41:34 - TradingSystem - INFO - [OK] Fetched 1439 3m candles for BTC
2025-05-27 12:41:34 - TradingSystem - INFO - [DEBUG] Processing 1439 rows for storage
2025-05-27 12:41:34 - TradingSystem - INFO - [DEBUG] DataFrame columns: ['open', 'high', 'low', 'close', 'volume', 'timeframe']
2025-05-27 12:41:34 - TradingSystem - INFO - [DEBUG] DataFrame index type: <class 'pandas._libs.tslibs.timestamps.Timestamp'>
2025-05-27 12:41:34 - TradingSystem - INFO - [DEBUG] Record 0: timestamp=2025-05-24 01:45:00, open=107538.0, timeframe=3m
2025-05-27 12:41:34 - TradingSystem - INFO - [DEBUG] Record 1: timestamp=2025-05-24 01:48:00, open=107537.5, timeframe=3m
2025-05-27 12:41:34 - TradingSystem - INFO - [DEBUG] Record 2: timestamp=2025-05-24 01:51:00, open=107546.0, timeframe=3m
2025-05-27 12:41:34 - TradingSystem - INFO - [DEBUG] Prepared 1439 records for insertion
2025-05-27 12:41:34 - TradingSystem - INFO - [OK] Stored 1439 market data records for BTCUSD
2025-05-27 12:41:34 - TradingSystem - INFO - [OK] Stored 1439 3m records
2025-05-27 12:41:34 - TradingSystem - INFO - [FETCH] Processing 15m timeframe...
2025-05-27 12:41:34 - TradingSystem - INFO - [CHUNKED_DEBUG] Timeframe: 15m, Days requested: 3, Max per chunk: 36
2025-05-27 12:41:34 - TradingSystem - INFO - [CHUNKED_DEBUG] Using single request for 3 days
2025-05-27 12:41:34 - TradingSystem - INFO - [DATA] Fetching data with params: {'resolution': '15m', 'start': 1748050894, 'end': 1748310094, 'symbol': 'BTCUSD'}
2025-05-27 12:41:35 - TradingSystem - INFO - [DATA] API Response keys: ['result', 'success']
2025-05-27 12:41:35 - TradingSystem - INFO - [DATA] Received 288 raw candles
2025-05-27 12:41:35 - TradingSystem - INFO - [DEBUG] Sample candle data: {'close': 108337.5, 'high': 108611, 'low': 108161.5, 'open': 108330, 'time': 1748309400, 'volume': 87229}
2025-05-27 12:41:35 - TradingSystem - INFO - [DEBUG] Candle length: 6
2025-05-27 12:41:35 - TradingSystem - WARNING - [WARNING] Removed 1 invalid candles
2025-05-27 12:41:35 - TradingSystem - INFO - [OK] Fetched 287 15m candles for BTC
2025-05-27 12:41:35 - TradingSystem - INFO - [DEBUG] Processing 287 rows for storage
2025-05-27 12:41:35 - TradingSystem - INFO - [DEBUG] DataFrame columns: ['open', 'high', 'low', 'close', 'volume', 'timeframe']
2025-05-27 12:41:35 - TradingSystem - INFO - [DEBUG] DataFrame index type: <class 'pandas._libs.tslibs.timestamps.Timestamp'>
2025-05-27 12:41:35 - TradingSystem - INFO - [DEBUG] Record 0: timestamp=2025-05-24 02:00:00, open=107448.0, timeframe=15m
2025-05-27 12:41:35 - TradingSystem - INFO - [DEBUG] Record 1: timestamp=2025-05-24 02:15:00, open=107578.5, timeframe=15m
2025-05-27 12:41:35 - TradingSystem - INFO - [DEBUG] Record 2: timestamp=2025-05-24 02:30:00, open=107757.0, timeframe=15m
2025-05-27 12:41:35 - TradingSystem - INFO - [DEBUG] Prepared 287 records for insertion
2025-05-27 12:41:35 - TradingSystem - INFO - [OK] Stored 287 market data records for BTCUSD
2025-05-27 12:41:35 - TradingSystem - INFO - [OK] Stored 287 15m records
2025-05-27 12:41:35 - TradingSystem - INFO - [FETCH] Processing 1h timeframe...
2025-05-27 12:41:35 - TradingSystem - INFO - [CHUNKED_DEBUG] Timeframe: 1h, Days requested: 3, Max per chunk: 145
2025-05-27 12:41:35 - TradingSystem - INFO - [CHUNKED_DEBUG] Using single request for 3 days
2025-05-27 12:41:35 - TradingSystem - INFO - [DATA] Fetching data with params: {'resolution': '1h', 'start': 1748050895, 'end': 1748310095, 'symbol': 'BTCUSD'}
2025-05-27 12:41:36 - TradingSystem - INFO - [DATA] API Response keys: ['result', 'success']
2025-05-27 12:41:36 - TradingSystem - INFO - [DATA] Received 72 raw candles
2025-05-27 12:41:36 - TradingSystem - INFO - [DEBUG] Sample candle data: {'close': 108365.5, 'high': 109116, 'low': 107460.5, 'open': 109115, 'time': 1748307600, 'volume': 476355}
2025-05-27 12:41:36 - TradingSystem - INFO - [DEBUG] Candle length: 6
2025-05-27 12:41:36 - TradingSystem - WARNING - [WARNING] Removed 1 invalid candles
2025-05-27 12:41:36 - TradingSystem - INFO - [OK] Fetched 71 1h candles for BTC
2025-05-27 12:41:36 - TradingSystem - INFO - [DEBUG] Processing 71 rows for storage
2025-05-27 12:41:36 - TradingSystem - INFO - [DEBUG] DataFrame columns: ['open', 'high', 'low', 'close', 'volume', 'timeframe']
2025-05-27 12:41:36 - TradingSystem - INFO - [DEBUG] DataFrame index type: <class 'pandas._libs.tslibs.timestamps.Timestamp'>
2025-05-27 12:41:36 - TradingSystem - INFO - [DEBUG] Record 0: timestamp=2025-05-24 03:00:00, open=107754.5, timeframe=1h
2025-05-27 12:41:36 - TradingSystem - INFO - [DEBUG] Record 1: timestamp=2025-05-24 04:00:00, open=108318.0, timeframe=1h
2025-05-27 12:41:36 - TradingSystem - INFO - [DEBUG] Record 2: timestamp=2025-05-24 05:00:00, open=108449.5, timeframe=1h
2025-05-27 12:41:36 - TradingSystem - INFO - [DEBUG] Prepared 71 records for insertion
2025-05-27 12:41:36 - TradingSystem - INFO - [OK] Stored 71 market data records for BTCUSD
2025-05-27 12:41:36 - TradingSystem - INFO - [OK] Stored 71 1h records
2025-05-27 12:41:36 - TradingSystem - INFO - [SUCCESS] Fetched and stored 1797 total records across 3 timeframes
2025-05-27 12:42:50 - TradingSystem - INFO - [START] Professional logging system initialized: TradingSystem
2025-05-27 12:42:50 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-05-27 12:42:50 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-05-27 12:42:50 - TradingSystem - INFO - [OK] Delta Exchange client initialized
2025-05-27 12:42:50 - TradingSystem - INFO - [OK] Found BTCUSD product: ID 27, type: None
2025-05-27 12:42:50 - TradingSystem - INFO - [OK] Market data feed initialized - BTC Product ID: 27
2025-05-27 12:42:50 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-05-27 12:42:50 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-05-27 12:42:50 - TradingSystem - INFO - [OK] Simple data fetcher initialized
2025-05-27 12:42:50 - TradingSystem - INFO - [START] Custom range fetch: 2025-05-20 to 2025-05-27 (8 days)
2025-05-27 12:42:50 - TradingSystem - INFO - [FETCH] Processing 3m timeframe...
2025-05-27 12:42:50 - TradingSystem - INFO - [CHUNKED_DEBUG] Timeframe: 3m, Days requested: 8, Max per chunk: 7
2025-05-27 12:42:50 - TradingSystem - INFO - [CHUNKED_DEBUG] Using chunked requests for 8 days
2025-05-27 12:42:50 - TradingSystem - INFO - [CHUNKED] Fetching 8 days of 3m data in 2 chunks
2025-05-27 12:42:50 - TradingSystem - INFO - [CHUNKED] Max days per chunk: 7
2025-05-27 12:42:50 - TradingSystem - INFO - [CHUNKED] Fetching chunk 1/2: 7 days ending at 2025-05-27 12:42:50.942606
2025-05-27 12:42:50 - TradingSystem - INFO - [DATA] Fetching data with params: {'resolution': '3m', 'start': 1747725170, 'end': 1748329970, 'symbol': 'BTCUSD'}
2025-05-27 12:42:51 - TradingSystem - INFO - [DATA] API Response keys: ['result', 'success']
2025-05-27 12:42:51 - TradingSystem - INFO - [DATA] Received 3359 raw candles
2025-05-27 12:42:51 - TradingSystem - INFO - [DEBUG] Sample candle data: {'close': 109035.5, 'high': 109073.5, 'low': 109006, 'open': 109070.5, 'time': 1748329740, 'volume': 6013}
2025-05-27 12:42:51 - TradingSystem - INFO - [DEBUG] Candle length: 6
2025-05-27 12:42:52 - TradingSystem - WARNING - [WARNING] Removed 1 invalid candles
2025-05-27 12:42:52 - TradingSystem - INFO - [OK] Fetched 3358 3m candles for BTC
2025-05-27 12:42:52 - TradingSystem - INFO - [CHUNKED] Chunk 1 successful: 3358 candles
2025-05-27 12:42:53 - TradingSystem - INFO - [CHUNKED] Fetching chunk 2/2: 1 days ending at 2025-05-20 12:42:50.942606
2025-05-27 12:42:53 - TradingSystem - INFO - [DATA] Fetching data with params: {'resolution': '3m', 'start': 1747638770, 'end': 1747725170, 'symbol': 'BTCUSD'}
2025-05-27 12:42:53 - TradingSystem - INFO - [DATA] API Response keys: ['result', 'success']
2025-05-27 12:42:53 - TradingSystem - INFO - [DATA] Received 480 raw candles
2025-05-27 12:42:53 - TradingSystem - INFO - [DEBUG] Sample candle data: {'close': 105643, 'high': 105710.5, 'low': 105610, 'open': 105633, 'time': 1747725120, 'volume': 21243}
2025-05-27 12:42:53 - TradingSystem - INFO - [DEBUG] Candle length: 6
2025-05-27 12:42:53 - TradingSystem - WARNING - [WARNING] Removed 1 invalid candles
2025-05-27 12:42:53 - TradingSystem - INFO - [OK] Fetched 479 3m candles for BTC
2025-05-27 12:42:53 - TradingSystem - INFO - [CHUNKED] Chunk 2 successful: 479 candles
2025-05-27 12:42:53 - TradingSystem - INFO - [CHUNKED] Combined 2 chunks into 3837 total candles
2025-05-27 12:42:53 - TradingSystem - INFO - [CHUNKED] Date range: 2025-05-19 07:18:00 to 2025-05-27 07:09:00
2025-05-27 12:42:53 - TradingSystem - INFO - [DEBUG] Processing 3249 rows for storage
2025-05-27 12:42:53 - TradingSystem - INFO - [DEBUG] DataFrame columns: ['open', 'high', 'low', 'close', 'volume', 'timeframe']
2025-05-27 12:42:53 - TradingSystem - INFO - [DEBUG] DataFrame index type: <class 'pandas._libs.tslibs.timestamps.Timestamp'>
2025-05-27 12:42:53 - TradingSystem - INFO - [DEBUG] Record 0: timestamp=2025-05-20 12:45:00, open=104677.0, timeframe=3m
2025-05-27 12:42:53 - TradingSystem - INFO - [DEBUG] Record 1: timestamp=2025-05-20 12:48:00, open=104668.5, timeframe=3m
2025-05-27 12:42:53 - TradingSystem - INFO - [DEBUG] Record 2: timestamp=2025-05-20 12:51:00, open=104657.5, timeframe=3m
2025-05-27 12:42:54 - TradingSystem - INFO - [DEBUG] Prepared 3249 records for insertion
2025-05-27 12:42:54 - TradingSystem - INFO - [OK] Stored 3249 market data records for BTCUSD
2025-05-27 12:42:54 - TradingSystem - INFO - [OK] Stored 3249 3m records
2025-05-27 12:42:54 - TradingSystem - INFO - [FETCH] Processing 15m timeframe...
2025-05-27 12:42:54 - TradingSystem - INFO - [CHUNKED_DEBUG] Timeframe: 15m, Days requested: 8, Max per chunk: 36
2025-05-27 12:42:54 - TradingSystem - INFO - [CHUNKED_DEBUG] Using single request for 8 days
2025-05-27 12:42:54 - TradingSystem - INFO - [DATA] Fetching data with params: {'resolution': '15m', 'start': 1747638770, 'end': 1748329970, 'symbol': 'BTCUSD'}
2025-05-27 12:42:54 - TradingSystem - INFO - [DATA] API Response keys: ['result', 'success']
2025-05-27 12:42:54 - TradingSystem - INFO - [DATA] Received 768 raw candles
2025-05-27 12:42:54 - TradingSystem - INFO - [DEBUG] Sample candle data: {'close': 109035.5, 'high': 109091, 'low': 108791, 'open': 108791, 'time': 1748329200, 'volume': 49088}
2025-05-27 12:42:54 - TradingSystem - INFO - [DEBUG] Candle length: 6
2025-05-27 12:42:55 - TradingSystem - WARNING - [WARNING] Removed 1 invalid candles
2025-05-27 12:42:55 - TradingSystem - INFO - [OK] Fetched 767 15m candles for BTC
2025-05-27 12:42:55 - TradingSystem - INFO - [DEBUG] Processing 650 rows for storage
2025-05-27 12:42:55 - TradingSystem - INFO - [DEBUG] DataFrame columns: ['open', 'high', 'low', 'close', 'volume', 'timeframe']
2025-05-27 12:42:55 - TradingSystem - INFO - [DEBUG] DataFrame index type: <class 'pandas._libs.tslibs.timestamps.Timestamp'>
2025-05-27 12:42:55 - TradingSystem - INFO - [DEBUG] Record 0: timestamp=2025-05-20 12:45:00, open=104677.0, timeframe=15m
2025-05-27 12:42:55 - TradingSystem - INFO - [DEBUG] Record 1: timestamp=2025-05-20 13:00:00, open=104729.5, timeframe=15m
2025-05-27 12:42:55 - TradingSystem - INFO - [DEBUG] Record 2: timestamp=2025-05-20 13:15:00, open=104544.5, timeframe=15m
2025-05-27 12:42:55 - TradingSystem - INFO - [DEBUG] Prepared 650 records for insertion
2025-05-27 12:42:55 - TradingSystem - INFO - [OK] Stored 650 market data records for BTCUSD
2025-05-27 12:42:55 - TradingSystem - INFO - [OK] Stored 650 15m records
2025-05-27 12:42:55 - TradingSystem - INFO - [FETCH] Processing 1h timeframe...
2025-05-27 12:42:55 - TradingSystem - INFO - [CHUNKED_DEBUG] Timeframe: 1h, Days requested: 8, Max per chunk: 145
2025-05-27 12:42:55 - TradingSystem - INFO - [CHUNKED_DEBUG] Using single request for 8 days
2025-05-27 12:42:55 - TradingSystem - INFO - [DATA] Fetching data with params: {'resolution': '1h', 'start': 1747638770, 'end': 1748329970, 'symbol': 'BTCUSD'}
2025-05-27 12:42:56 - TradingSystem - INFO - [DATA] API Response keys: ['result', 'success']
2025-05-27 12:42:56 - TradingSystem - INFO - [DATA] Received 192 raw candles
2025-05-27 12:42:56 - TradingSystem - INFO - [DEBUG] Sample candle data: {'close': 109035.5, 'high': 109091, 'low': 108791, 'open': 108791, 'time': 1748329200, 'volume': 49088}
2025-05-27 12:42:56 - TradingSystem - INFO - [DEBUG] Candle length: 6
2025-05-27 12:42:56 - TradingSystem - WARNING - [WARNING] Removed 1 invalid candles
2025-05-27 12:42:56 - TradingSystem - INFO - [OK] Fetched 191 1h candles for BTC
2025-05-27 12:42:56 - TradingSystem - INFO - [DEBUG] Processing 163 rows for storage
2025-05-27 12:42:56 - TradingSystem - INFO - [DEBUG] DataFrame columns: ['open', 'high', 'low', 'close', 'volume', 'timeframe']
2025-05-27 12:42:56 - TradingSystem - INFO - [DEBUG] DataFrame index type: <class 'pandas._libs.tslibs.timestamps.Timestamp'>
2025-05-27 12:42:56 - TradingSystem - INFO - [DEBUG] Record 0: timestamp=2025-05-20 13:00:00, open=104729.5, timeframe=1h
2025-05-27 12:42:56 - TradingSystem - INFO - [DEBUG] Record 1: timestamp=2025-05-20 14:00:00, open=104406.0, timeframe=1h
2025-05-27 12:42:56 - TradingSystem - INFO - [DEBUG] Record 2: timestamp=2025-05-20 15:00:00, open=104475.0, timeframe=1h
2025-05-27 12:42:57 - TradingSystem - INFO - [DEBUG] Prepared 163 records for insertion
2025-05-27 12:42:57 - TradingSystem - INFO - [OK] Stored 163 market data records for BTCUSD
2025-05-27 12:42:57 - TradingSystem - INFO - [OK] Stored 163 1h records
2025-05-27 12:42:57 - TradingSystem - INFO - [SUCCESS] Custom range fetch completed: 4062 total records
2025-05-27 12:42:57 - TradingSystem - INFO - [OK] Retrieved 3 records for BTCUSD 3m
2025-05-27 12:42:57 - TradingSystem - INFO - [OK] Retrieved 3 records for BTCUSD 15m
2025-05-27 12:42:57 - TradingSystem - INFO - [OK] Retrieved 3 records for BTCUSD 1h
2025-05-27 12:42:57 - TradingSystem - INFO - [START] Custom range fetch: 2025-04-27 to 2025-05-27 (31 days)
2025-05-27 12:42:57 - TradingSystem - INFO - [FETCH] Processing 3m timeframe...
2025-05-27 12:42:57 - TradingSystem - INFO - [OK] Retrieved 1 records for BTCUSD 3m
2025-05-27 12:42:57 - TradingSystem - INFO - [SKIP] 3m data exists for this range, skipping
2025-05-27 12:42:57 - TradingSystem - INFO - [FETCH] Processing 15m timeframe...
2025-05-27 12:42:57 - TradingSystem - INFO - [OK] Retrieved 1 records for BTCUSD 15m
2025-05-27 12:42:57 - TradingSystem - INFO - [SKIP] 15m data exists for this range, skipping
2025-05-27 12:42:57 - TradingSystem - INFO - [FETCH] Processing 1h timeframe...
2025-05-27 12:42:57 - TradingSystem - INFO - [OK] Retrieved 1 records for BTCUSD 1h
2025-05-27 12:42:57 - TradingSystem - INFO - [SKIP] 1h data exists for this range, skipping
2025-05-27 12:42:57 - TradingSystem - INFO - [SUCCESS] Custom range fetch completed: 0 total records
2025-05-27 12:48:45 - TradingSystem - INFO - [START] Professional logging system initialized: TradingSystem
2025-05-27 12:48:45 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-05-27 12:48:45 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-05-27 12:59:34 - TradingSystem - INFO - [START] Professional logging system initialized: TradingSystem
2025-05-27 12:59:34 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-05-27 12:59:34 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-05-27 12:59:34 - TradingSystem - INFO - [OK] Delta Exchange client initialized
2025-05-27 12:59:34 - TradingSystem - INFO - [OK] Found BTCUSD product: ID 27, type: None
2025-05-27 12:59:34 - TradingSystem - INFO - [OK] Market data feed initialized - BTC Product ID: 27
2025-05-27 12:59:34 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-05-27 12:59:34 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-05-27 12:59:34 - TradingSystem - INFO - [OK] Simple data fetcher initialized
2025-05-27 12:59:34 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-05-27 12:59:34 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-05-27 12:59:34 - TradingSystem - INFO - [OK] Delta Exchange client initialized
2025-05-27 12:59:35 - TradingSystem - INFO - [OK] Found BTCUSD product: ID 27, type: None
2025-05-27 12:59:35 - TradingSystem - INFO - [OK] Market data feed initialized - BTC Product ID: 27
2025-05-27 12:59:35 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-05-27 12:59:35 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-05-27 12:59:35 - TradingSystem - INFO - [OK] Simple data fetcher initialized
2025-05-27 13:10:43 - TradingSystem - INFO - [START] Professional logging system initialized: TradingSystem
2025-05-27 13:10:43 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-05-27 13:10:43 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-05-27 13:10:43 - TradingSystem - INFO - [OK] Delta Exchange client initialized
2025-05-27 13:10:43 - TradingSystem - INFO - [OK] Found BTCUSD product: ID 27, type: None
2025-05-27 13:10:43 - TradingSystem - INFO - [OK] Market data feed initialized - BTC Product ID: 27
2025-05-27 13:10:43 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-05-27 13:10:43 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-05-27 13:10:43 - TradingSystem - INFO - [OK] Simple data fetcher initialized
2025-05-27 13:10:43 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-05-27 13:10:43 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-05-27 13:10:43 - TradingSystem - INFO - [OK] Delta Exchange client initialized
2025-05-27 13:10:44 - TradingSystem - INFO - [OK] Found BTCUSD product: ID 27, type: None
2025-05-27 13:10:44 - TradingSystem - INFO - [OK] Market data feed initialized - BTC Product ID: 27
2025-05-27 13:10:44 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-05-27 13:10:44 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-05-27 13:10:44 - TradingSystem - INFO - [OK] Simple data fetcher initialized
2025-05-27 17:02:19 - TradingSystem - INFO - [START] Professional logging system initialized: TradingSystem
2025-05-27 17:02:19 - TradingSystem - INFO - [PATH] Using database path: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-05-27 17:02:19 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-05-27 17:02:19 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-06-02 17:10:05 - TradingSystem - INFO - [START] Professional logging system initialized: TradingSystem
2025-06-02 17:10:05 - TradingSystem - INFO - [PATH] Config file: C:\Agent_Trading\Agent_Trading\config.json
2025-06-02 17:10:05 - TradingSystem - INFO - [PATH] Config db_path: crypto_market/data/crypto_trading.db
2025-06-02 17:10:05 - TradingSystem - INFO - [PATH] Resolved database path: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-06-02 17:10:05 - TradingSystem - INFO - [PATH] Database exists: True
2025-06-02 17:10:05 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-06-02 17:10:05 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
