2025-05-27 14:51:46 - TradingSystem - INFO - [START] Professional logging system initialized: TradingSystem
2025-05-27 14:51:46 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-05-27 14:51:46 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-05-27 14:51:46 - TradingSystem - INFO - [OK] Delta Exchange client initialized
2025-05-27 14:51:46 - TradingSystem - INFO - [OK] Found BTCUSD product: ID 27, type: None
2025-05-27 14:51:46 - TradingSystem - INFO - [OK] Market data feed initialized - BTC Product ID: 27
2025-05-27 14:51:46 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-05-27 14:51:46 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-05-27 14:51:46 - TradingSystem - INFO - [OK] Simple data fetcher initialized
2025-05-27 14:51:57 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-05-27 14:51:57 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-05-27 14:51:57 - TradingSystem - INFO - [OK] Delta Exchange client initialized
2025-05-27 14:51:57 - TradingSystem - INFO - [OK] Found BTCUSD product: ID 27, type: None
2025-05-27 14:51:57 - TradingSystem - INFO - [OK] Market data feed initialized - BTC Product ID: 27
2025-05-27 14:51:57 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-05-27 14:51:57 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-05-27 14:51:57 - TradingSystem - INFO - [OK] Simple data fetcher initialized
2025-05-27 14:51:57 - TradingSystem - INFO - [OK] Retrieved 100 records for BTCUSD 1h
2025-05-27 14:52:04 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-05-27 14:52:04 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-05-27 14:52:04 - TradingSystem - INFO - [OK] Delta Exchange client initialized
2025-05-27 14:52:04 - TradingSystem - INFO - [OK] Found BTCUSD product: ID 27, type: None
2025-05-27 14:52:04 - TradingSystem - INFO - [OK] Market data feed initialized - BTC Product ID: 27
2025-05-27 14:52:04 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-05-27 14:52:04 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-05-27 14:52:04 - TradingSystem - INFO - [OK] Simple data fetcher initialized
2025-05-27 14:52:04 - TradingSystem - INFO - [OK] Retrieved 100 records for BTCUSD 1h
2025-05-27 14:52:10 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-05-27 14:52:10 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-05-27 14:52:10 - TradingSystem - INFO - [OK] Delta Exchange client initialized
2025-05-27 14:52:10 - TradingSystem - INFO - [OK] Found BTCUSD product: ID 27, type: None
2025-05-27 14:52:10 - TradingSystem - INFO - [OK] Market data feed initialized - BTC Product ID: 27
2025-05-27 14:52:10 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-05-27 14:52:10 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-05-27 14:52:10 - TradingSystem - INFO - [OK] Simple data fetcher initialized
2025-05-27 14:52:10 - TradingSystem - INFO - [OK] Retrieved 100 records for BTCUSD 1h
2025-05-27 14:54:09 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-05-27 14:54:09 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-05-27 14:54:09 - TradingSystem - INFO - [OK] Delta Exchange client initialized
2025-05-27 14:54:10 - TradingSystem - INFO - [OK] Found BTCUSD product: ID 27, type: None
2025-05-27 14:54:10 - TradingSystem - INFO - [OK] Market data feed initialized - BTC Product ID: 27
2025-05-27 14:54:10 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-05-27 14:54:10 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-05-27 14:54:10 - TradingSystem - INFO - [OK] Simple data fetcher initialized
2025-05-27 14:54:10 - TradingSystem - INFO - [OK] Retrieved 100 records for BTCUSD 1h
2025-05-27 14:54:11 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-05-27 14:54:11 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-05-27 14:54:11 - TradingSystem - INFO - [OK] Delta Exchange client initialized
2025-05-27 14:54:12 - TradingSystem - INFO - [OK] Found BTCUSD product: ID 27, type: None
2025-05-27 14:54:12 - TradingSystem - INFO - [OK] Market data feed initialized - BTC Product ID: 27
2025-05-27 14:54:12 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-05-27 14:54:12 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-05-27 14:54:12 - TradingSystem - INFO - [OK] Simple data fetcher initialized
2025-05-27 14:54:12 - TradingSystem - INFO - [OK] Retrieved 100 records for BTCUSD 1h
2025-05-27 14:56:21 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-05-27 14:56:21 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-05-27 14:56:21 - TradingSystem - INFO - [OK] Delta Exchange client initialized
2025-05-27 14:56:21 - TradingSystem - INFO - [OK] Found BTCUSD product: ID 27, type: None
2025-05-27 14:56:22 - TradingSystem - INFO - [OK] Market data feed initialized - BTC Product ID: 27
2025-05-27 14:56:22 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-05-27 14:56:22 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-05-27 14:56:22 - TradingSystem - INFO - [OK] Simple data fetcher initialized
2025-05-27 14:56:25 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-05-27 14:56:25 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-05-27 14:56:25 - TradingSystem - INFO - [OK] Delta Exchange client initialized
2025-05-27 14:56:26 - TradingSystem - INFO - [OK] Found BTCUSD product: ID 27, type: None
2025-05-27 14:56:26 - TradingSystem - INFO - [OK] Market data feed initialized - BTC Product ID: 27
2025-05-27 14:56:26 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-05-27 14:56:26 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-05-27 14:56:26 - TradingSystem - INFO - [OK] Simple data fetcher initialized
2025-05-27 14:56:26 - TradingSystem - INFO - [OK] Retrieved 100 records for BTCUSD 1h
2025-05-27 14:56:34 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-05-27 14:56:34 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-05-27 14:56:34 - TradingSystem - INFO - [OK] Delta Exchange client initialized
2025-05-27 14:56:34 - TradingSystem - INFO - [OK] Found BTCUSD product: ID 27, type: None
2025-05-27 14:56:34 - TradingSystem - INFO - [OK] Market data feed initialized - BTC Product ID: 27
2025-05-27 14:56:35 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-05-27 14:56:35 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-05-27 14:56:35 - TradingSystem - INFO - [OK] Simple data fetcher initialized
2025-05-27 14:56:35 - TradingSystem - INFO - [OK] Retrieved 100 records for BTCUSD 1h
2025-05-27 14:56:37 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-05-27 14:56:37 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-05-27 14:56:37 - TradingSystem - INFO - [OK] Delta Exchange client initialized
2025-05-27 14:56:37 - TradingSystem - INFO - [OK] Found BTCUSD product: ID 27, type: None
2025-05-27 14:56:37 - TradingSystem - INFO - [OK] Market data feed initialized - BTC Product ID: 27
2025-05-27 14:56:37 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-05-27 14:56:37 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-05-27 14:56:37 - TradingSystem - INFO - [OK] Simple data fetcher initialized
2025-05-27 14:56:37 - TradingSystem - INFO - [OK] Retrieved 100 records for BTCUSD 1h
2025-05-27 14:56:48 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-05-27 14:56:48 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-05-27 14:56:48 - TradingSystem - INFO - [OK] Delta Exchange client initialized
2025-05-27 14:56:48 - TradingSystem - INFO - [OK] Found BTCUSD product: ID 27, type: None
2025-05-27 14:56:48 - TradingSystem - INFO - [OK] Market data feed initialized - BTC Product ID: 27
2025-05-27 14:56:48 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-05-27 14:56:48 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-05-27 14:56:48 - TradingSystem - INFO - [OK] Simple data fetcher initialized
2025-05-27 14:56:48 - TradingSystem - INFO - [OK] Retrieved 100 records for BTCUSD 1h
2025-05-27 14:56:50 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-05-27 14:56:50 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-05-27 14:56:50 - TradingSystem - INFO - [OK] Delta Exchange client initialized
2025-05-27 14:56:50 - TradingSystem - INFO - [OK] Found BTCUSD product: ID 27, type: None
2025-05-27 14:56:50 - TradingSystem - INFO - [OK] Market data feed initialized - BTC Product ID: 27
2025-05-27 14:56:50 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-05-27 14:56:50 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-05-27 14:56:50 - TradingSystem - INFO - [OK] Simple data fetcher initialized
2025-05-27 14:56:50 - TradingSystem - INFO - [OK] Retrieved 100 records for BTCUSD 1h
2025-05-27 15:02:04 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-05-27 15:02:04 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-05-27 15:02:04 - TradingSystem - INFO - [OK] Delta Exchange client initialized
2025-05-27 15:02:04 - TradingSystem - INFO - [OK] Found BTCUSD product: ID 27, type: None
2025-05-27 15:02:04 - TradingSystem - INFO - [OK] Market data feed initialized - BTC Product ID: 27
2025-05-27 15:02:04 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-05-27 15:02:04 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-05-27 15:02:04 - TradingSystem - INFO - [OK] Simple data fetcher initialized
2025-05-27 15:02:04 - TradingSystem - INFO - [OK] Retrieved 100 records for BTCUSD 1h
2025-05-27 15:02:07 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-05-27 15:02:07 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-05-27 15:02:07 - TradingSystem - INFO - [OK] Delta Exchange client initialized
2025-05-27 15:02:07 - TradingSystem - INFO - [OK] Found BTCUSD product: ID 27, type: None
2025-05-27 15:02:07 - TradingSystem - INFO - [OK] Market data feed initialized - BTC Product ID: 27
2025-05-27 15:02:07 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-05-27 15:02:07 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-05-27 15:02:07 - TradingSystem - INFO - [OK] Simple data fetcher initialized
2025-05-27 15:02:07 - TradingSystem - INFO - [OK] Retrieved 100 records for BTCUSD 1h
2025-05-27 15:02:14 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-05-27 15:02:14 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-05-27 15:02:14 - TradingSystem - INFO - [OK] Delta Exchange client initialized
2025-05-27 15:02:14 - TradingSystem - INFO - [OK] Found BTCUSD product: ID 27, type: None
2025-05-27 15:02:14 - TradingSystem - INFO - [OK] Market data feed initialized - BTC Product ID: 27
2025-05-27 15:02:14 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-05-27 15:02:14 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-05-27 15:02:14 - TradingSystem - INFO - [OK] Simple data fetcher initialized
2025-05-27 15:02:20 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-05-27 15:02:20 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-05-27 15:02:20 - TradingSystem - INFO - [OK] Delta Exchange client initialized
2025-05-27 15:02:20 - TradingSystem - INFO - [OK] Found BTCUSD product: ID 27, type: None
2025-05-27 15:02:20 - TradingSystem - INFO - [OK] Market data feed initialized - BTC Product ID: 27
2025-05-27 15:02:20 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-05-27 15:02:20 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-05-27 15:02:20 - TradingSystem - INFO - [OK] Simple data fetcher initialized
2025-05-27 15:02:20 - TradingSystem - INFO - [OK] Retrieved 100 records for BTCUSD 1h
2025-05-27 15:02:23 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-05-27 15:02:23 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-05-27 15:02:23 - TradingSystem - INFO - [OK] Delta Exchange client initialized
2025-05-27 15:02:23 - TradingSystem - INFO - [OK] Found BTCUSD product: ID 27, type: None
2025-05-27 15:02:23 - TradingSystem - INFO - [OK] Market data feed initialized - BTC Product ID: 27
2025-05-27 15:02:23 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-05-27 15:02:23 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-05-27 15:02:23 - TradingSystem - INFO - [OK] Simple data fetcher initialized
2025-05-27 15:02:23 - TradingSystem - INFO - [OK] Retrieved 100 records for BTCUSD 1h
2025-05-27 15:02:24 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-05-27 15:02:24 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-05-27 15:02:24 - TradingSystem - INFO - [OK] Delta Exchange client initialized
2025-05-27 15:02:24 - TradingSystem - INFO - [OK] Found BTCUSD product: ID 27, type: None
2025-05-27 15:02:24 - TradingSystem - INFO - [OK] Market data feed initialized - BTC Product ID: 27
2025-05-27 15:02:24 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-05-27 15:02:24 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-05-27 15:02:24 - TradingSystem - INFO - [OK] Simple data fetcher initialized
2025-05-27 15:02:25 - TradingSystem - INFO - [OK] Retrieved 100 records for BTCUSD 1h
2025-05-27 15:02:28 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-05-27 15:02:28 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-05-27 15:02:28 - TradingSystem - INFO - [OK] Delta Exchange client initialized
2025-05-27 15:02:28 - TradingSystem - INFO - [OK] Found BTCUSD product: ID 27, type: None
2025-05-27 15:02:28 - TradingSystem - INFO - [OK] Market data feed initialized - BTC Product ID: 27
2025-05-27 15:02:28 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-05-27 15:02:28 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-05-27 15:02:28 - TradingSystem - INFO - [OK] Simple data fetcher initialized
2025-05-27 15:02:28 - TradingSystem - INFO - [OK] Retrieved 100 records for BTCUSD 1h
2025-05-27 15:04:42 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-05-27 15:04:42 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-05-27 15:04:42 - TradingSystem - INFO - [OK] Delta Exchange client initialized
2025-05-27 15:04:42 - TradingSystem - INFO - [OK] Found BTCUSD product: ID 27, type: None
2025-05-27 15:04:42 - TradingSystem - INFO - [OK] Market data feed initialized - BTC Product ID: 27
2025-05-27 15:04:42 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-05-27 15:04:42 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-05-27 15:04:42 - TradingSystem - INFO - [OK] Simple data fetcher initialized
2025-05-27 15:04:46 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-05-27 15:04:46 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-05-27 15:04:46 - TradingSystem - INFO - [OK] Delta Exchange client initialized
2025-05-27 15:04:46 - TradingSystem - INFO - [OK] Found BTCUSD product: ID 27, type: None
2025-05-27 15:04:46 - TradingSystem - INFO - [OK] Market data feed initialized - BTC Product ID: 27
2025-05-27 15:04:46 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-05-27 15:04:46 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-05-27 15:04:46 - TradingSystem - INFO - [OK] Simple data fetcher initialized
2025-05-27 15:04:46 - TradingSystem - INFO - [OK] Retrieved 100 records for BTCUSD 1h
2025-05-27 15:04:49 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-05-27 15:04:49 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-05-27 15:04:49 - TradingSystem - INFO - [OK] Delta Exchange client initialized
2025-05-27 15:04:49 - TradingSystem - INFO - [OK] Found BTCUSD product: ID 27, type: None
2025-05-27 15:04:49 - TradingSystem - INFO - [OK] Market data feed initialized - BTC Product ID: 27
2025-05-27 15:04:49 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-05-27 15:04:49 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-05-27 15:04:49 - TradingSystem - INFO - [OK] Simple data fetcher initialized
2025-05-27 15:04:49 - TradingSystem - INFO - [OK] Retrieved 100 records for BTCUSD 1h
2025-05-27 15:04:50 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-05-27 15:04:50 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-05-27 15:04:50 - TradingSystem - INFO - [OK] Delta Exchange client initialized
2025-05-27 15:04:50 - TradingSystem - INFO - [OK] Found BTCUSD product: ID 27, type: None
2025-05-27 15:04:50 - TradingSystem - INFO - [OK] Market data feed initialized - BTC Product ID: 27
2025-05-27 15:04:50 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-05-27 15:04:50 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-05-27 15:04:50 - TradingSystem - INFO - [OK] Simple data fetcher initialized
2025-05-27 15:04:50 - TradingSystem - INFO - [OK] Retrieved 100 records for BTCUSD 1h
2025-05-27 15:04:54 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-05-27 15:04:54 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-05-27 15:04:54 - TradingSystem - INFO - [OK] Delta Exchange client initialized
2025-05-27 15:04:54 - TradingSystem - INFO - [OK] Found BTCUSD product: ID 27, type: None
2025-05-27 15:04:54 - TradingSystem - INFO - [OK] Market data feed initialized - BTC Product ID: 27
2025-05-27 15:04:54 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-05-27 15:04:54 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-05-27 15:04:54 - TradingSystem - INFO - [OK] Simple data fetcher initialized
2025-05-27 15:04:55 - TradingSystem - INFO - [OK] Retrieved 100 records for BTCUSD 1h
2025-05-27 15:04:57 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-05-27 15:04:57 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-05-27 15:04:57 - TradingSystem - INFO - [OK] Delta Exchange client initialized
2025-05-27 15:04:57 - TradingSystem - INFO - [OK] Found BTCUSD product: ID 27, type: None
2025-05-27 15:04:57 - TradingSystem - INFO - [OK] Market data feed initialized - BTC Product ID: 27
2025-05-27 15:04:57 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-05-27 15:04:57 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-05-27 15:04:57 - TradingSystem - INFO - [OK] Simple data fetcher initialized
2025-05-27 15:04:57 - TradingSystem - INFO - [OK] Retrieved 100 records for BTCUSD 1h
2025-05-27 15:04:59 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-05-27 15:04:59 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-05-27 15:04:59 - TradingSystem - INFO - [OK] Delta Exchange client initialized
2025-05-27 15:05:00 - TradingSystem - INFO - [OK] Found BTCUSD product: ID 27, type: None
2025-05-27 15:05:00 - TradingSystem - INFO - [OK] Market data feed initialized - BTC Product ID: 27
2025-05-27 15:05:00 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-05-27 15:05:00 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-05-27 15:05:00 - TradingSystem - INFO - [OK] Simple data fetcher initialized
2025-05-27 15:05:00 - TradingSystem - INFO - [OK] Retrieved 100 records for BTCUSD 1h
2025-05-27 15:05:02 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-05-27 15:05:02 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-05-27 15:05:02 - TradingSystem - INFO - [OK] Delta Exchange client initialized
2025-05-27 15:05:03 - TradingSystem - INFO - [OK] Found BTCUSD product: ID 27, type: None
2025-05-27 15:05:03 - TradingSystem - INFO - [OK] Market data feed initialized - BTC Product ID: 27
2025-05-27 15:05:03 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-05-27 15:05:03 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-05-27 15:05:03 - TradingSystem - INFO - [OK] Simple data fetcher initialized
2025-05-27 15:05:03 - TradingSystem - INFO - [OK] Retrieved 100 records for BTCUSD 1h
2025-05-27 15:05:04 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-05-27 15:05:04 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-05-27 15:05:04 - TradingSystem - INFO - [OK] Delta Exchange client initialized
2025-05-27 15:05:04 - TradingSystem - INFO - [OK] Found BTCUSD product: ID 27, type: None
2025-05-27 15:05:04 - TradingSystem - INFO - [OK] Market data feed initialized - BTC Product ID: 27
2025-05-27 15:05:04 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-05-27 15:05:04 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-05-27 15:05:04 - TradingSystem - INFO - [OK] Simple data fetcher initialized
2025-05-27 15:05:04 - TradingSystem - INFO - [OK] Retrieved 100 records for BTCUSD 1h
2025-05-27 15:15:20 - TradingSystem - INFO - [START] Professional logging system initialized: TradingSystem
2025-05-27 15:15:20 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-05-27 15:15:20 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\crypto_market\data\crypto_trading.db
2025-05-27 15:15:20 - TradingSystem - INFO - [OK] Delta Exchange client initialized
2025-05-27 15:15:20 - TradingSystem - INFO - [OK] Found BTCUSD product: ID 27, type: None
2025-05-27 15:15:20 - TradingSystem - INFO - [OK] Market data feed initialized - BTC Product ID: 27
2025-05-27 15:15:20 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-05-27 15:15:20 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\crypto_market\data\crypto_trading.db
2025-05-27 15:15:20 - TradingSystem - INFO - [OK] Simple data fetcher initialized
2025-05-27 15:15:26 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-05-27 15:15:26 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\crypto_market\data\crypto_trading.db
2025-05-27 15:15:26 - TradingSystem - INFO - [OK] Delta Exchange client initialized
2025-05-27 15:15:26 - TradingSystem - INFO - [OK] Found BTCUSD product: ID 27, type: None
2025-05-27 15:15:26 - TradingSystem - INFO - [OK] Market data feed initialized - BTC Product ID: 27
2025-05-27 15:15:26 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-05-27 15:15:26 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\crypto_market\data\crypto_trading.db
2025-05-27 15:15:26 - TradingSystem - INFO - [OK] Simple data fetcher initialized
2025-05-27 15:15:26 - TradingSystem - INFO - [OK] Retrieved 0 records for BTCUSD 1h
2025-05-27 15:15:42 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-05-27 15:15:42 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\crypto_market\data\crypto_trading.db
2025-05-27 15:15:42 - TradingSystem - INFO - [OK] Delta Exchange client initialized
2025-05-27 15:15:42 - TradingSystem - INFO - [OK] Found BTCUSD product: ID 27, type: None
2025-05-27 15:15:42 - TradingSystem - INFO - [OK] Market data feed initialized - BTC Product ID: 27
2025-05-27 15:15:42 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-05-27 15:15:42 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\crypto_market\data\crypto_trading.db
2025-05-27 15:15:42 - TradingSystem - INFO - [OK] Simple data fetcher initialized
2025-05-27 15:15:42 - TradingSystem - INFO - [OK] Retrieved 0 records for BTCUSD 1h
2025-05-27 15:15:45 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-05-27 15:15:45 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\crypto_market\data\crypto_trading.db
2025-05-27 15:15:45 - TradingSystem - INFO - [OK] Delta Exchange client initialized
2025-05-27 15:15:45 - TradingSystem - INFO - [OK] Found BTCUSD product: ID 27, type: None
2025-05-27 15:15:45 - TradingSystem - INFO - [OK] Market data feed initialized - BTC Product ID: 27
2025-05-27 15:15:45 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-05-27 15:15:45 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\crypto_market\data\crypto_trading.db
2025-05-27 15:15:45 - TradingSystem - INFO - [OK] Simple data fetcher initialized
2025-05-27 15:15:45 - TradingSystem - INFO - [OK] Retrieved 0 records for BTCUSD 1h
2025-05-27 15:15:54 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-05-27 15:15:54 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\crypto_market\data\crypto_trading.db
2025-05-27 15:15:54 - TradingSystem - INFO - [OK] Delta Exchange client initialized
2025-05-27 15:15:54 - TradingSystem - INFO - [OK] Found BTCUSD product: ID 27, type: None
2025-05-27 15:15:54 - TradingSystem - INFO - [OK] Market data feed initialized - BTC Product ID: 27
2025-05-27 15:15:54 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-05-27 15:15:54 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\crypto_market\data\crypto_trading.db
2025-05-27 15:15:54 - TradingSystem - INFO - [OK] Simple data fetcher initialized
2025-05-27 15:15:54 - TradingSystem - INFO - [OK] Retrieved 0 records for BTCUSD 1h
2025-05-27 15:19:34 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-05-27 15:19:34 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\crypto_market\data\crypto_trading.db
2025-05-27 15:19:34 - TradingSystem - INFO - [OK] Delta Exchange client initialized
2025-05-27 15:19:34 - TradingSystem - INFO - [OK] Found BTCUSD product: ID 27, type: None
2025-05-27 15:19:34 - TradingSystem - INFO - [OK] Market data feed initialized - BTC Product ID: 27
2025-05-27 15:19:34 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-05-27 15:19:34 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\crypto_market\data\crypto_trading.db
2025-05-27 15:19:34 - TradingSystem - INFO - [OK] Simple data fetcher initialized
2025-05-27 15:19:37 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-05-27 15:19:37 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\crypto_market\data\crypto_trading.db
2025-05-27 15:19:37 - TradingSystem - INFO - [OK] Delta Exchange client initialized
2025-05-27 15:19:37 - TradingSystem - INFO - [OK] Found BTCUSD product: ID 27, type: None
2025-05-27 15:19:37 - TradingSystem - INFO - [OK] Market data feed initialized - BTC Product ID: 27
2025-05-27 15:19:37 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-05-27 15:19:37 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\crypto_market\data\crypto_trading.db
2025-05-27 15:19:37 - TradingSystem - INFO - [OK] Simple data fetcher initialized
2025-05-27 15:19:37 - TradingSystem - INFO - [OK] Retrieved 0 records for BTCUSD 1h
2025-05-27 15:19:43 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-05-27 15:19:43 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\crypto_market\data\crypto_trading.db
2025-05-27 15:19:43 - TradingSystem - INFO - [OK] Delta Exchange client initialized
2025-05-27 15:19:44 - TradingSystem - INFO - [OK] Found BTCUSD product: ID 27, type: None
2025-05-27 15:19:44 - TradingSystem - INFO - [OK] Market data feed initialized - BTC Product ID: 27
2025-05-27 15:19:44 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-05-27 15:19:44 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\crypto_market\data\crypto_trading.db
2025-05-27 15:19:44 - TradingSystem - INFO - [OK] Simple data fetcher initialized
2025-05-27 15:19:44 - TradingSystem - INFO - [OK] Retrieved 0 records for BTCUSD 1h
2025-05-27 15:19:45 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-05-27 15:19:45 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\crypto_market\data\crypto_trading.db
2025-05-27 15:19:45 - TradingSystem - INFO - [OK] Delta Exchange client initialized
2025-05-27 15:19:45 - TradingSystem - INFO - [OK] Found BTCUSD product: ID 27, type: None
2025-05-27 15:19:45 - TradingSystem - INFO - [OK] Market data feed initialized - BTC Product ID: 27
2025-05-27 15:19:45 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-05-27 15:19:45 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\crypto_market\data\crypto_trading.db
2025-05-27 15:19:45 - TradingSystem - INFO - [OK] Simple data fetcher initialized
2025-05-27 15:19:45 - TradingSystem - INFO - [OK] Retrieved 0 records for BTCUSD 1h
2025-05-27 15:19:54 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-05-27 15:19:54 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\crypto_market\data\crypto_trading.db
2025-05-27 15:19:54 - TradingSystem - INFO - [OK] Delta Exchange client initialized
2025-05-27 15:19:54 - TradingSystem - INFO - [OK] Found BTCUSD product: ID 27, type: None
2025-05-27 15:19:54 - TradingSystem - INFO - [OK] Market data feed initialized - BTC Product ID: 27
2025-05-27 15:19:54 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-05-27 15:19:54 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\crypto_market\data\crypto_trading.db
2025-05-27 15:19:54 - TradingSystem - INFO - [OK] Simple data fetcher initialized
2025-05-27 15:19:54 - TradingSystem - INFO - [OK] Retrieved 0 records for BTCUSD 1h
2025-05-27 15:19:56 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-05-27 15:19:56 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\crypto_market\data\crypto_trading.db
2025-05-27 15:19:56 - TradingSystem - INFO - [OK] Delta Exchange client initialized
2025-05-27 15:19:56 - TradingSystem - INFO - [OK] Found BTCUSD product: ID 27, type: None
2025-05-27 15:19:56 - TradingSystem - INFO - [OK] Market data feed initialized - BTC Product ID: 27
2025-05-27 15:19:56 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-05-27 15:19:56 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\crypto_market\data\crypto_trading.db
2025-05-27 15:19:56 - TradingSystem - INFO - [OK] Simple data fetcher initialized
2025-05-27 15:19:56 - TradingSystem - INFO - [OK] Retrieved 0 records for BTCUSD 1h
2025-05-27 15:20:03 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-05-27 15:20:03 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\crypto_market\data\crypto_trading.db
2025-05-27 15:20:03 - TradingSystem - INFO - [OK] Delta Exchange client initialized
2025-05-27 15:20:03 - TradingSystem - INFO - [OK] Found BTCUSD product: ID 27, type: None
2025-05-27 15:20:03 - TradingSystem - INFO - [OK] Market data feed initialized - BTC Product ID: 27
2025-05-27 15:20:03 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-05-27 15:20:03 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\crypto_market\data\crypto_trading.db
2025-05-27 15:20:03 - TradingSystem - INFO - [OK] Simple data fetcher initialized
2025-05-27 15:20:03 - TradingSystem - INFO - [OK] Retrieved 0 records for BTCUSD 1h
2025-05-27 15:20:05 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-05-27 15:20:05 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\crypto_market\data\crypto_trading.db
2025-05-27 15:20:05 - TradingSystem - INFO - [OK] Delta Exchange client initialized
2025-05-27 15:20:05 - TradingSystem - INFO - [OK] Found BTCUSD product: ID 27, type: None
2025-05-27 15:20:05 - TradingSystem - INFO - [OK] Market data feed initialized - BTC Product ID: 27
2025-05-27 15:20:05 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-05-27 15:20:05 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\crypto_market\data\crypto_trading.db
2025-05-27 15:20:05 - TradingSystem - INFO - [OK] Simple data fetcher initialized
2025-05-27 15:20:05 - TradingSystem - INFO - [OK] Retrieved 0 records for BTCUSD 1h
2025-05-27 15:38:31 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-05-27 15:38:31 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\crypto_market\data\crypto_trading.db
2025-05-27 15:38:31 - TradingSystem - INFO - [OK] Delta Exchange client initialized
2025-05-27 15:38:31 - TradingSystem - INFO - [OK] Found BTCUSD product: ID 27, type: None
2025-05-27 15:38:31 - TradingSystem - INFO - [OK] Market data feed initialized - BTC Product ID: 27
2025-05-27 15:38:31 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-05-27 15:38:31 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\crypto_market\data\crypto_trading.db
2025-05-27 15:38:31 - TradingSystem - INFO - [OK] Simple data fetcher initialized
2025-05-27 15:38:35 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-05-27 15:38:35 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\crypto_market\data\crypto_trading.db
2025-05-27 15:38:35 - TradingSystem - INFO - [OK] Delta Exchange client initialized
2025-05-27 15:38:36 - TradingSystem - INFO - [OK] Found BTCUSD product: ID 27, type: None
2025-05-27 15:38:36 - TradingSystem - INFO - [OK] Market data feed initialized - BTC Product ID: 27
2025-05-27 15:38:36 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-05-27 15:38:36 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\crypto_market\data\crypto_trading.db
2025-05-27 15:38:36 - TradingSystem - INFO - [OK] Simple data fetcher initialized
2025-05-27 15:38:36 - TradingSystem - INFO - [OK] Retrieved 0 records for BTCUSD 1h
2025-05-27 15:38:40 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-05-27 15:38:40 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\crypto_market\data\crypto_trading.db
2025-05-27 15:38:40 - TradingSystem - INFO - [OK] Delta Exchange client initialized
2025-05-27 15:38:40 - TradingSystem - INFO - [OK] Found BTCUSD product: ID 27, type: None
2025-05-27 15:38:40 - TradingSystem - INFO - [OK] Market data feed initialized - BTC Product ID: 27
2025-05-27 15:38:40 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-05-27 15:38:40 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\crypto_market\data\crypto_trading.db
2025-05-27 15:38:40 - TradingSystem - INFO - [OK] Simple data fetcher initialized
2025-05-27 15:38:40 - TradingSystem - INFO - [OK] Retrieved 0 records for BTCUSD 1h
2025-05-27 15:38:42 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-05-27 15:38:42 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\crypto_market\data\crypto_trading.db
2025-05-27 15:38:42 - TradingSystem - INFO - [OK] Delta Exchange client initialized
2025-05-27 15:38:42 - TradingSystem - INFO - [OK] Found BTCUSD product: ID 27, type: None
2025-05-27 15:38:42 - TradingSystem - INFO - [OK] Market data feed initialized - BTC Product ID: 27
2025-05-27 15:38:42 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-05-27 15:38:42 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\crypto_market\data\crypto_trading.db
2025-05-27 15:38:42 - TradingSystem - INFO - [OK] Simple data fetcher initialized
2025-05-27 15:38:42 - TradingSystem - INFO - [OK] Retrieved 0 records for BTCUSD 1h
2025-05-27 15:38:50 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-05-27 15:38:50 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\crypto_market\data\crypto_trading.db
2025-05-27 15:38:50 - TradingSystem - INFO - [OK] Delta Exchange client initialized
2025-05-27 15:38:50 - TradingSystem - INFO - [OK] Found BTCUSD product: ID 27, type: None
2025-05-27 15:38:50 - TradingSystem - INFO - [OK] Market data feed initialized - BTC Product ID: 27
2025-05-27 15:38:50 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-05-27 15:38:50 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\crypto_market\data\crypto_trading.db
2025-05-27 15:38:50 - TradingSystem - INFO - [OK] Simple data fetcher initialized
2025-05-27 15:38:50 - TradingSystem - INFO - [OK] Retrieved 0 records for BTCUSD 1h
2025-05-27 15:38:54 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-05-27 15:38:54 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\crypto_market\data\crypto_trading.db
2025-05-27 15:38:54 - TradingSystem - INFO - [OK] Delta Exchange client initialized
2025-05-27 15:38:55 - TradingSystem - INFO - [OK] Found BTCUSD product: ID 27, type: None
2025-05-27 15:38:55 - TradingSystem - INFO - [OK] Market data feed initialized - BTC Product ID: 27
2025-05-27 15:38:55 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-05-27 15:38:55 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\crypto_market\data\crypto_trading.db
2025-05-27 15:38:55 - TradingSystem - INFO - [OK] Simple data fetcher initialized
2025-05-27 15:38:55 - TradingSystem - INFO - [OK] Retrieved 0 records for BTCUSD 1h
2025-05-27 15:38:57 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-05-27 15:38:57 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\crypto_market\data\crypto_trading.db
2025-05-27 15:38:57 - TradingSystem - INFO - [OK] Delta Exchange client initialized
2025-05-27 15:38:58 - TradingSystem - INFO - [OK] Found BTCUSD product: ID 27, type: None
2025-05-27 15:38:58 - TradingSystem - INFO - [OK] Market data feed initialized - BTC Product ID: 27
2025-05-27 15:38:58 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-05-27 15:38:58 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\crypto_market\data\crypto_trading.db
2025-05-27 15:38:58 - TradingSystem - INFO - [OK] Simple data fetcher initialized
2025-05-27 15:38:59 - TradingSystem - INFO - [OK] Retrieved 0 records for BTCUSD 1h
2025-05-27 15:39:05 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-05-27 15:39:05 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\crypto_market\data\crypto_trading.db
2025-05-27 15:39:05 - TradingSystem - INFO - [OK] Delta Exchange client initialized
2025-05-27 15:39:05 - TradingSystem - INFO - [OK] Found BTCUSD product: ID 27, type: None
2025-05-27 15:39:05 - TradingSystem - INFO - [OK] Market data feed initialized - BTC Product ID: 27
2025-05-27 15:39:05 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-05-27 15:39:05 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\crypto_market\data\crypto_trading.db
2025-05-27 15:39:05 - TradingSystem - INFO - [OK] Simple data fetcher initialized
2025-05-27 15:39:05 - TradingSystem - INFO - [OK] Retrieved 0 records for BTCUSD 1h
2025-05-27 15:39:10 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-05-27 15:39:10 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\crypto_market\data\crypto_trading.db
2025-05-27 15:39:10 - TradingSystem - INFO - [OK] Delta Exchange client initialized
2025-05-27 15:39:10 - TradingSystem - INFO - [OK] Found BTCUSD product: ID 27, type: None
2025-05-27 15:39:10 - TradingSystem - INFO - [OK] Market data feed initialized - BTC Product ID: 27
2025-05-27 15:39:10 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-05-27 15:39:10 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\crypto_market\data\crypto_trading.db
2025-05-27 15:39:10 - TradingSystem - INFO - [OK] Simple data fetcher initialized
2025-05-27 15:39:10 - TradingSystem - INFO - [OK] Retrieved 0 records for BTCUSD 1h
2025-05-27 15:39:24 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-05-27 15:39:24 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\crypto_market\data\crypto_trading.db
2025-05-27 15:39:24 - TradingSystem - INFO - [OK] Delta Exchange client initialized
2025-05-27 15:39:24 - TradingSystem - INFO - [OK] Found BTCUSD product: ID 27, type: None
2025-05-27 15:39:24 - TradingSystem - INFO - [OK] Market data feed initialized - BTC Product ID: 27
2025-05-27 15:39:24 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-05-27 15:39:24 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\crypto_market\data\crypto_trading.db
2025-05-27 15:39:24 - TradingSystem - INFO - [OK] Simple data fetcher initialized
2025-05-27 15:39:24 - TradingSystem - INFO - [OK] Retrieved 0 records for BTCUSD 1h
2025-05-27 15:44:26 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-05-27 15:44:26 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-05-27 15:44:26 - TradingSystem - INFO - [OK] Delta Exchange client initialized
2025-05-27 15:44:28 - TradingSystem - INFO - [OK] Found BTCUSD product: ID 27, type: None
2025-05-27 15:44:28 - TradingSystem - INFO - [OK] Market data feed initialized - BTC Product ID: 27
2025-05-27 15:44:28 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-05-27 15:44:28 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-05-27 15:44:28 - TradingSystem - INFO - [OK] Simple data fetcher initialized
2025-05-27 15:44:38 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-05-27 15:44:38 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-05-27 15:44:38 - TradingSystem - INFO - [OK] Delta Exchange client initialized
2025-05-27 15:44:39 - TradingSystem - INFO - [OK] Found BTCUSD product: ID 27, type: None
2025-05-27 15:44:39 - TradingSystem - INFO - [OK] Market data feed initialized - BTC Product ID: 27
2025-05-27 15:44:39 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-05-27 15:44:39 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-05-27 15:44:39 - TradingSystem - INFO - [OK] Simple data fetcher initialized
2025-05-27 15:44:42 - TradingSystem - INFO - [OK] Retrieved 100 records for BTCUSD 1h
2025-05-27 15:44:44 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-05-27 15:44:44 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-05-27 15:44:44 - TradingSystem - INFO - [OK] Delta Exchange client initialized
2025-05-27 15:44:44 - TradingSystem - INFO - [OK] Found BTCUSD product: ID 27, type: None
2025-05-27 15:44:44 - TradingSystem - INFO - [OK] Market data feed initialized - BTC Product ID: 27
2025-05-27 15:44:44 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-05-27 15:44:44 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-05-27 15:44:44 - TradingSystem - INFO - [OK] Simple data fetcher initialized
2025-05-27 15:44:45 - TradingSystem - INFO - [OK] Retrieved 100 records for BTCUSD 1h
2025-05-27 15:44:47 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-05-27 15:44:47 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-05-27 15:44:47 - TradingSystem - INFO - [OK] Delta Exchange client initialized
2025-05-27 15:44:47 - TradingSystem - INFO - [OK] Found BTCUSD product: ID 27, type: None
2025-05-27 15:44:47 - TradingSystem - INFO - [OK] Market data feed initialized - BTC Product ID: 27
2025-05-27 15:44:47 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-05-27 15:44:47 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-05-27 15:44:47 - TradingSystem - INFO - [OK] Simple data fetcher initialized
2025-05-27 15:44:48 - TradingSystem - INFO - [OK] Retrieved 100 records for BTCUSD 1h
2025-05-27 15:45:15 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-05-27 15:45:15 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-05-27 15:45:15 - TradingSystem - INFO - [OK] Delta Exchange client initialized
2025-05-27 15:45:15 - TradingSystem - INFO - [OK] Found BTCUSD product: ID 27, type: None
2025-05-27 15:45:15 - TradingSystem - INFO - [OK] Market data feed initialized - BTC Product ID: 27
2025-05-27 15:45:15 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-05-27 15:45:15 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-05-27 15:45:15 - TradingSystem - INFO - [OK] Simple data fetcher initialized
2025-05-27 15:45:15 - TradingSystem - INFO - [OK] Retrieved 100 records for BTCUSD 1h
2025-05-27 15:45:17 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-05-27 15:45:17 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-05-27 15:45:17 - TradingSystem - INFO - [OK] Delta Exchange client initialized
2025-05-27 15:45:18 - TradingSystem - INFO - [OK] Found BTCUSD product: ID 27, type: None
2025-05-27 15:45:18 - TradingSystem - INFO - [OK] Market data feed initialized - BTC Product ID: 27
2025-05-27 15:45:18 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-05-27 15:45:18 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-05-27 15:45:18 - TradingSystem - INFO - [OK] Simple data fetcher initialized
2025-05-27 15:45:19 - TradingSystem - INFO - [OK] Retrieved 100 records for BTCUSD 1h
2025-05-27 15:45:35 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-05-27 15:45:35 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-05-27 15:45:35 - TradingSystem - INFO - [OK] Delta Exchange client initialized
2025-05-27 15:45:35 - TradingSystem - INFO - [OK] Found BTCUSD product: ID 27, type: None
2025-05-27 15:45:35 - TradingSystem - INFO - [OK] Market data feed initialized - BTC Product ID: 27
2025-05-27 15:45:35 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-05-27 15:45:35 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-05-27 15:45:35 - TradingSystem - INFO - [OK] Simple data fetcher initialized
2025-05-27 15:45:35 - TradingSystem - INFO - [OK] Retrieved 0 records for BTCUSD 1h
2025-06-02 13:59:31 - TradingSystem - INFO - [START] Professional logging system initialized: TradingSystem
2025-06-02 13:59:31 - TradingSystem - INFO - [PATH] Config file: C:\Agent_Trading\Agent_Trading\config.json
2025-06-02 13:59:31 - TradingSystem - INFO - [PATH] Config db_path: crypto_market/data/crypto_trading.db
2025-06-02 13:59:31 - TradingSystem - INFO - [PATH] Resolved database path: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-06-02 13:59:31 - TradingSystem - INFO - [PATH] Database exists: True
2025-06-02 13:59:31 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-06-02 13:59:31 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-06-02 13:59:31 - TradingSystem - INFO - [OK] Delta Exchange client initialized
2025-06-02 13:59:31 - TradingSystem - INFO - [OK] Found BTCUSD product: ID 27, type: None
2025-06-02 13:59:31 - TradingSystem - INFO - [OK] Market data feed initialized - BTC Product ID: 27
2025-06-02 13:59:31 - TradingSystem - INFO - [PATH] Config file: C:\Agent_Trading\Agent_Trading\config.json
2025-06-02 13:59:31 - TradingSystem - INFO - [PATH] Config db_path: crypto_market/data/crypto_trading.db
2025-06-02 13:59:31 - TradingSystem - INFO - [PATH] Resolved database path: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-06-02 13:59:31 - TradingSystem - INFO - [PATH] Database exists: True
2025-06-02 13:59:31 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-06-02 13:59:31 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-06-02 13:59:31 - TradingSystem - INFO - [OK] Simple data fetcher initialized
2025-06-02 13:59:32 - TradingSystem - INFO - [PATH] Config file: C:\Agent_Trading\Agent_Trading\config.json
2025-06-02 13:59:32 - TradingSystem - INFO - [PATH] Config db_path: crypto_market/data/crypto_trading.db
2025-06-02 13:59:32 - TradingSystem - INFO - [PATH] Resolved database path: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-06-02 13:59:32 - TradingSystem - INFO - [PATH] Database exists: True
2025-06-02 13:59:32 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-06-02 13:59:32 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-06-02 13:59:32 - TradingSystem - INFO - [OK] Delta Exchange client initialized
2025-06-02 13:59:32 - TradingSystem - INFO - [OK] Found BTCUSD product: ID 27, type: None
2025-06-02 13:59:32 - TradingSystem - INFO - [OK] Market data feed initialized - BTC Product ID: 27
2025-06-02 13:59:32 - TradingSystem - INFO - [PATH] Config file: C:\Agent_Trading\Agent_Trading\config.json
2025-06-02 13:59:32 - TradingSystem - INFO - [PATH] Config db_path: crypto_market/data/crypto_trading.db
2025-06-02 13:59:32 - TradingSystem - INFO - [PATH] Resolved database path: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-06-02 13:59:32 - TradingSystem - INFO - [PATH] Database exists: True
2025-06-02 13:59:32 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-06-02 13:59:32 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-06-02 13:59:32 - TradingSystem - INFO - [OK] Simple data fetcher initialized
2025-06-02 13:59:42 - TradingSystem - INFO - [PATH] Config file: C:\Agent_Trading\Agent_Trading\config.json
2025-06-02 13:59:42 - TradingSystem - INFO - [PATH] Config db_path: crypto_market/data/crypto_trading.db
2025-06-02 13:59:42 - TradingSystem - INFO - [PATH] Resolved database path: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-06-02 13:59:42 - TradingSystem - INFO - [PATH] Database exists: True
2025-06-02 13:59:42 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-06-02 13:59:42 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-06-02 13:59:42 - TradingSystem - INFO - [OK] Delta Exchange client initialized
2025-06-02 13:59:42 - TradingSystem - INFO - [OK] Found BTCUSD product: ID 27, type: None
2025-06-02 13:59:42 - TradingSystem - INFO - [OK] Market data feed initialized - BTC Product ID: 27
2025-06-02 13:59:42 - TradingSystem - INFO - [PATH] Config file: C:\Agent_Trading\Agent_Trading\config.json
2025-06-02 13:59:42 - TradingSystem - INFO - [PATH] Config db_path: crypto_market/data/crypto_trading.db
2025-06-02 13:59:42 - TradingSystem - INFO - [PATH] Resolved database path: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-06-02 13:59:42 - TradingSystem - INFO - [PATH] Database exists: True
2025-06-02 13:59:42 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-06-02 13:59:42 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-06-02 13:59:42 - TradingSystem - INFO - [OK] Simple data fetcher initialized
2025-06-02 13:59:57 - TradingSystem - INFO - [PATH] Config file: C:\Agent_Trading\Agent_Trading\config.json
2025-06-02 13:59:57 - TradingSystem - INFO - [PATH] Config db_path: crypto_market/data/crypto_trading.db
2025-06-02 13:59:57 - TradingSystem - INFO - [PATH] Resolved database path: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-06-02 13:59:57 - TradingSystem - INFO - [PATH] Database exists: True
2025-06-02 13:59:57 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-06-02 13:59:57 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-06-02 13:59:57 - TradingSystem - INFO - [OK] Delta Exchange client initialized
2025-06-02 13:59:57 - TradingSystem - INFO - [OK] Found BTCUSD product: ID 27, type: None
2025-06-02 13:59:57 - TradingSystem - INFO - [OK] Market data feed initialized - BTC Product ID: 27
2025-06-02 13:59:57 - TradingSystem - INFO - [PATH] Config file: C:\Agent_Trading\Agent_Trading\config.json
2025-06-02 13:59:57 - TradingSystem - INFO - [PATH] Config db_path: crypto_market/data/crypto_trading.db
2025-06-02 13:59:57 - TradingSystem - INFO - [PATH] Resolved database path: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-06-02 13:59:57 - TradingSystem - INFO - [PATH] Database exists: True
2025-06-02 13:59:57 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-06-02 13:59:57 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-06-02 13:59:57 - TradingSystem - INFO - [OK] Simple data fetcher initialized
2025-06-02 14:34:24 - TradingSystem - INFO - [PATH] Config file: C:\Agent_Trading\Agent_Trading\config.json
2025-06-02 14:34:24 - TradingSystem - INFO - [PATH] Config db_path: crypto_market/data/crypto_trading.db
2025-06-02 14:34:24 - TradingSystem - INFO - [PATH] Resolved database path: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-06-02 14:34:24 - TradingSystem - INFO - [PATH] Database exists: True
2025-06-02 14:34:24 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-06-02 14:34:24 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-06-02 14:34:24 - TradingSystem - INFO - [OK] Delta Exchange client initialized
2025-06-02 14:34:24 - TradingSystem - INFO - [OK] Found BTCUSD product: ID 27, type: None
2025-06-02 14:34:24 - TradingSystem - INFO - [OK] Market data feed initialized - BTC Product ID: 27
2025-06-02 14:34:24 - TradingSystem - INFO - [PATH] Config file: C:\Agent_Trading\Agent_Trading\config.json
2025-06-02 14:34:24 - TradingSystem - INFO - [PATH] Config db_path: crypto_market/data/crypto_trading.db
2025-06-02 14:34:24 - TradingSystem - INFO - [PATH] Resolved database path: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-06-02 14:34:24 - TradingSystem - INFO - [PATH] Database exists: True
2025-06-02 14:34:24 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-06-02 14:34:24 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-06-02 14:34:24 - TradingSystem - INFO - [OK] Simple data fetcher initialized
2025-06-02 14:34:26 - TradingSystem - INFO - [PATH] Config file: C:\Agent_Trading\Agent_Trading\config.json
2025-06-02 14:34:26 - TradingSystem - INFO - [PATH] Config db_path: crypto_market/data/crypto_trading.db
2025-06-02 14:34:26 - TradingSystem - INFO - [PATH] Resolved database path: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-06-02 14:34:26 - TradingSystem - INFO - [PATH] Database exists: True
2025-06-02 14:34:26 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-06-02 14:34:26 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-06-02 14:34:26 - TradingSystem - INFO - [OK] Delta Exchange client initialized
2025-06-02 14:34:26 - TradingSystem - INFO - [OK] Found BTCUSD product: ID 27, type: None
2025-06-02 14:34:26 - TradingSystem - INFO - [OK] Market data feed initialized - BTC Product ID: 27
2025-06-02 14:34:26 - TradingSystem - INFO - [PATH] Config file: C:\Agent_Trading\Agent_Trading\config.json
2025-06-02 14:34:26 - TradingSystem - INFO - [PATH] Config db_path: crypto_market/data/crypto_trading.db
2025-06-02 14:34:26 - TradingSystem - INFO - [PATH] Resolved database path: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-06-02 14:34:26 - TradingSystem - INFO - [PATH] Database exists: True
2025-06-02 14:34:27 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-06-02 14:34:27 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-06-02 14:34:27 - TradingSystem - INFO - [OK] Simple data fetcher initialized
2025-06-02 14:50:36 - TradingSystem - INFO - [PATH] Config file: C:\Agent_Trading\Agent_Trading\config.json
2025-06-02 14:50:36 - TradingSystem - INFO - [PATH] Config db_path: crypto_market/data/crypto_trading.db
2025-06-02 14:50:36 - TradingSystem - INFO - [PATH] Resolved database path: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-06-02 14:50:36 - TradingSystem - INFO - [PATH] Database exists: True
2025-06-02 14:50:36 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-06-02 14:50:36 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-06-02 14:50:36 - TradingSystem - INFO - [OK] Delta Exchange client initialized
2025-06-02 14:50:37 - TradingSystem - INFO - [OK] Found BTCUSD product: ID 27, type: None
2025-06-02 14:50:37 - TradingSystem - INFO - [OK] Market data feed initialized - BTC Product ID: 27
2025-06-02 14:50:37 - TradingSystem - INFO - [PATH] Config file: C:\Agent_Trading\Agent_Trading\config.json
2025-06-02 14:50:37 - TradingSystem - INFO - [PATH] Config db_path: crypto_market/data/crypto_trading.db
2025-06-02 14:50:37 - TradingSystem - INFO - [PATH] Resolved database path: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-06-02 14:50:37 - TradingSystem - INFO - [PATH] Database exists: True
2025-06-02 14:50:37 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-06-02 14:50:37 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-06-02 14:50:37 - TradingSystem - INFO - [OK] Simple data fetcher initialized
2025-06-02 14:50:44 - TradingSystem - INFO - [PATH] Config file: C:\Agent_Trading\Agent_Trading\config.json
2025-06-02 14:50:44 - TradingSystem - INFO - [PATH] Config db_path: crypto_market/data/crypto_trading.db
2025-06-02 14:50:44 - TradingSystem - INFO - [PATH] Resolved database path: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-06-02 14:50:44 - TradingSystem - INFO - [PATH] Database exists: True
2025-06-02 14:50:44 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-06-02 14:50:44 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-06-02 14:50:44 - TradingSystem - INFO - [OK] Delta Exchange client initialized
2025-06-02 14:50:44 - TradingSystem - INFO - [OK] Found BTCUSD product: ID 27, type: None
2025-06-02 14:50:44 - TradingSystem - INFO - [OK] Market data feed initialized - BTC Product ID: 27
2025-06-02 14:50:44 - TradingSystem - INFO - [PATH] Config file: C:\Agent_Trading\Agent_Trading\config.json
2025-06-02 14:50:44 - TradingSystem - INFO - [PATH] Config db_path: crypto_market/data/crypto_trading.db
2025-06-02 14:50:44 - TradingSystem - INFO - [PATH] Resolved database path: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-06-02 14:50:44 - TradingSystem - INFO - [PATH] Database exists: True
2025-06-02 14:50:44 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-06-02 14:50:44 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-06-02 14:50:44 - TradingSystem - INFO - [OK] Simple data fetcher initialized
2025-06-02 14:51:22 - TradingSystem - INFO - [PATH] Config file: C:\Agent_Trading\Agent_Trading\config.json
2025-06-02 14:51:22 - TradingSystem - INFO - [PATH] Config db_path: crypto_market/data/crypto_trading.db
2025-06-02 14:51:22 - TradingSystem - INFO - [PATH] Resolved database path: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-06-02 14:51:22 - TradingSystem - INFO - [PATH] Database exists: True
2025-06-02 14:51:22 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-06-02 14:51:22 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-06-02 14:51:22 - TradingSystem - INFO - [OK] Delta Exchange client initialized
2025-06-02 14:51:22 - TradingSystem - INFO - [OK] Found BTCUSD product: ID 27, type: None
2025-06-02 14:51:22 - TradingSystem - INFO - [OK] Market data feed initialized - BTC Product ID: 27
2025-06-02 14:51:22 - TradingSystem - INFO - [PATH] Config file: C:\Agent_Trading\Agent_Trading\config.json
2025-06-02 14:51:22 - TradingSystem - INFO - [PATH] Config db_path: crypto_market/data/crypto_trading.db
2025-06-02 14:51:22 - TradingSystem - INFO - [PATH] Resolved database path: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-06-02 14:51:22 - TradingSystem - INFO - [PATH] Database exists: True
2025-06-02 14:51:22 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-06-02 14:51:22 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-06-02 14:51:22 - TradingSystem - INFO - [OK] Simple data fetcher initialized
2025-06-02 14:51:22 - TradingSystem - INFO - [PATH] Config file: C:\Agent_Trading\Agent_Trading\config.json
2025-06-02 14:51:22 - TradingSystem - INFO - [PATH] Config db_path: crypto_market/data/crypto_trading.db
2025-06-02 14:51:22 - TradingSystem - INFO - [PATH] Resolved database path: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-06-02 14:51:22 - TradingSystem - INFO - [PATH] Database exists: True
2025-06-02 14:51:22 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-06-02 14:51:22 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-06-02 14:51:24 - TradingSystem - INFO - [OK] Retrieved 780 records for BTCUSD 3m
2025-06-02 14:51:25 - TradingSystem - INFO - [OK] Retrieved 156 records for BTCUSD 15m
2025-06-02 14:51:25 - TradingSystem - INFO - [OK] Retrieved 39 records for BTCUSD 1h
2025-06-02 15:36:35 - TradingSystem - INFO - [PATH] Config file: C:\Agent_Trading\Agent_Trading\config.json
2025-06-02 15:36:35 - TradingSystem - INFO - [PATH] Config db_path: crypto_market/data/crypto_trading.db
2025-06-02 15:36:35 - TradingSystem - INFO - [PATH] Resolved database path: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-06-02 15:36:35 - TradingSystem - INFO - [PATH] Database exists: True
2025-06-02 15:36:35 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-06-02 15:36:35 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-06-02 15:36:35 - TradingSystem - INFO - [OK] Delta Exchange client initialized
2025-06-02 15:36:36 - TradingSystem - INFO - [OK] Found BTCUSD product: ID 27, type: None
2025-06-02 15:36:36 - TradingSystem - INFO - [OK] Market data feed initialized - BTC Product ID: 27
2025-06-02 15:36:36 - TradingSystem - INFO - [PATH] Config file: C:\Agent_Trading\Agent_Trading\config.json
2025-06-02 15:36:36 - TradingSystem - INFO - [PATH] Config db_path: crypto_market/data/crypto_trading.db
2025-06-02 15:36:36 - TradingSystem - INFO - [PATH] Resolved database path: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-06-02 15:36:36 - TradingSystem - INFO - [PATH] Database exists: True
2025-06-02 15:36:36 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-06-02 15:36:36 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-06-02 15:36:36 - TradingSystem - INFO - [OK] Simple data fetcher initialized
2025-06-02 15:36:38 - TradingSystem - INFO - [PATH] Config file: C:\Agent_Trading\Agent_Trading\config.json
2025-06-02 15:36:38 - TradingSystem - INFO - [PATH] Config db_path: crypto_market/data/crypto_trading.db
2025-06-02 15:36:38 - TradingSystem - INFO - [PATH] Resolved database path: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-06-02 15:36:38 - TradingSystem - INFO - [PATH] Database exists: True
2025-06-02 15:36:38 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-06-02 15:36:38 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-06-02 15:36:38 - TradingSystem - INFO - [OK] Delta Exchange client initialized
2025-06-02 15:36:39 - TradingSystem - INFO - [OK] Found BTCUSD product: ID 27, type: None
2025-06-02 15:36:39 - TradingSystem - INFO - [OK] Market data feed initialized - BTC Product ID: 27
2025-06-02 15:36:39 - TradingSystem - INFO - [PATH] Config file: C:\Agent_Trading\Agent_Trading\config.json
2025-06-02 15:36:39 - TradingSystem - INFO - [PATH] Config db_path: crypto_market/data/crypto_trading.db
2025-06-02 15:36:39 - TradingSystem - INFO - [PATH] Resolved database path: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-06-02 15:36:39 - TradingSystem - INFO - [PATH] Database exists: True
2025-06-02 15:36:39 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-06-02 15:36:39 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-06-02 15:36:39 - TradingSystem - INFO - [OK] Simple data fetcher initialized
2025-06-02 15:36:41 - TradingSystem - INFO - [OK] Retrieved 100 records for BTCUSD 1h
2025-06-02 15:36:46 - TradingSystem - INFO - [PATH] Config file: C:\Agent_Trading\Agent_Trading\config.json
2025-06-02 15:36:46 - TradingSystem - INFO - [PATH] Config db_path: crypto_market/data/crypto_trading.db
2025-06-02 15:36:46 - TradingSystem - INFO - [PATH] Resolved database path: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-06-02 15:36:46 - TradingSystem - INFO - [PATH] Database exists: True
2025-06-02 15:36:46 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-06-02 15:36:46 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-06-02 15:36:46 - TradingSystem - INFO - [OK] Delta Exchange client initialized
2025-06-02 15:36:46 - TradingSystem - INFO - [OK] Found BTCUSD product: ID 27, type: None
2025-06-02 15:36:46 - TradingSystem - INFO - [OK] Market data feed initialized - BTC Product ID: 27
2025-06-02 15:36:46 - TradingSystem - INFO - [PATH] Config file: C:\Agent_Trading\Agent_Trading\config.json
2025-06-02 15:36:46 - TradingSystem - INFO - [PATH] Config db_path: crypto_market/data/crypto_trading.db
2025-06-02 15:36:46 - TradingSystem - INFO - [PATH] Resolved database path: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-06-02 15:36:46 - TradingSystem - INFO - [PATH] Database exists: True
2025-06-02 15:36:46 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-06-02 15:36:46 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-06-02 15:36:46 - TradingSystem - INFO - [OK] Simple data fetcher initialized
2025-06-02 15:36:51 - TradingSystem - INFO - [PATH] Config file: C:\Agent_Trading\Agent_Trading\config.json
2025-06-02 15:36:51 - TradingSystem - INFO - [PATH] Config db_path: crypto_market/data/crypto_trading.db
2025-06-02 15:36:51 - TradingSystem - INFO - [PATH] Resolved database path: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-06-02 15:36:51 - TradingSystem - INFO - [PATH] Database exists: True
2025-06-02 15:36:51 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-06-02 15:36:51 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-06-02 15:36:51 - TradingSystem - INFO - [OK] Delta Exchange client initialized
2025-06-02 15:36:52 - TradingSystem - INFO - [OK] Found BTCUSD product: ID 27, type: None
2025-06-02 15:36:52 - TradingSystem - INFO - [OK] Market data feed initialized - BTC Product ID: 27
2025-06-02 15:36:52 - TradingSystem - INFO - [PATH] Config file: C:\Agent_Trading\Agent_Trading\config.json
2025-06-02 15:36:52 - TradingSystem - INFO - [PATH] Config db_path: crypto_market/data/crypto_trading.db
2025-06-02 15:36:52 - TradingSystem - INFO - [PATH] Resolved database path: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-06-02 15:36:52 - TradingSystem - INFO - [PATH] Database exists: True
2025-06-02 15:36:52 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-06-02 15:36:52 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-06-02 15:36:52 - TradingSystem - INFO - [OK] Simple data fetcher initialized
2025-06-02 15:42:22 - TradingSystem - INFO - [PATH] Config file: C:\Agent_Trading\Agent_Trading\config.json
2025-06-02 15:42:22 - TradingSystem - INFO - [PATH] Config db_path: crypto_market/data/crypto_trading.db
2025-06-02 15:42:22 - TradingSystem - INFO - [PATH] Resolved database path: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-06-02 15:42:22 - TradingSystem - INFO - [PATH] Database exists: True
2025-06-02 15:42:22 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-06-02 15:42:22 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-06-02 15:42:22 - TradingSystem - INFO - [OK] Delta Exchange client initialized
2025-06-02 15:42:23 - TradingSystem - INFO - [OK] Found BTCUSD product: ID 27, type: None
2025-06-02 15:42:23 - TradingSystem - INFO - [OK] Market data feed initialized - BTC Product ID: 27
2025-06-02 15:42:23 - TradingSystem - INFO - [PATH] Config file: C:\Agent_Trading\Agent_Trading\config.json
2025-06-02 15:42:23 - TradingSystem - INFO - [PATH] Config db_path: crypto_market/data/crypto_trading.db
2025-06-02 15:42:23 - TradingSystem - INFO - [PATH] Resolved database path: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-06-02 15:42:23 - TradingSystem - INFO - [PATH] Database exists: True
2025-06-02 15:42:23 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-06-02 15:42:23 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-06-02 15:42:23 - TradingSystem - INFO - [OK] Simple data fetcher initialized
2025-06-02 15:42:23 - TradingSystem - INFO - [PATH] Config file: C:\Agent_Trading\Agent_Trading\config.json
2025-06-02 15:42:23 - TradingSystem - INFO - [PATH] Config db_path: crypto_market/data/crypto_trading.db
2025-06-02 15:42:23 - TradingSystem - INFO - [PATH] Resolved database path: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-06-02 15:42:23 - TradingSystem - INFO - [PATH] Database exists: True
2025-06-02 15:42:23 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-06-02 15:42:23 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-06-02 15:42:23 - TradingSystem - INFO - [OK] Retrieved 763 records for BTCUSD 3m
2025-06-02 15:42:23 - TradingSystem - INFO - [OK] Retrieved 153 records for BTCUSD 15m
2025-06-02 15:42:23 - TradingSystem - INFO - [OK] Retrieved 38 records for BTCUSD 1h
2025-06-02 15:42:27 - TradingSystem - INFO - [PATH] Config file: C:\Agent_Trading\Agent_Trading\config.json
2025-06-02 15:42:27 - TradingSystem - INFO - [PATH] Config db_path: crypto_market/data/crypto_trading.db
2025-06-02 15:42:27 - TradingSystem - INFO - [PATH] Resolved database path: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-06-02 15:42:27 - TradingSystem - INFO - [PATH] Database exists: True
2025-06-02 15:42:27 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-06-02 15:42:27 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-06-02 15:42:27 - TradingSystem - INFO - [OK] Delta Exchange client initialized
2025-06-02 15:42:28 - TradingSystem - INFO - [OK] Found BTCUSD product: ID 27, type: None
2025-06-02 15:42:28 - TradingSystem - INFO - [OK] Market data feed initialized - BTC Product ID: 27
2025-06-02 15:42:28 - TradingSystem - INFO - [PATH] Config file: C:\Agent_Trading\Agent_Trading\config.json
2025-06-02 15:42:28 - TradingSystem - INFO - [PATH] Config db_path: crypto_market/data/crypto_trading.db
2025-06-02 15:42:28 - TradingSystem - INFO - [PATH] Resolved database path: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-06-02 15:42:28 - TradingSystem - INFO - [PATH] Database exists: True
2025-06-02 15:42:28 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-06-02 15:42:28 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-06-02 15:42:28 - TradingSystem - INFO - [OK] Simple data fetcher initialized
2025-06-02 15:42:28 - TradingSystem - INFO - [PATH] Config file: C:\Agent_Trading\Agent_Trading\config.json
2025-06-02 15:42:28 - TradingSystem - INFO - [PATH] Config db_path: crypto_market/data/crypto_trading.db
2025-06-02 15:42:28 - TradingSystem - INFO - [PATH] Resolved database path: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-06-02 15:42:28 - TradingSystem - INFO - [PATH] Database exists: True
2025-06-02 15:42:28 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-06-02 15:42:28 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-06-02 15:42:28 - TradingSystem - INFO - [OK] Retrieved 763 records for BTCUSD 3m
2025-06-02 15:42:28 - TradingSystem - INFO - [OK] Retrieved 153 records for BTCUSD 15m
2025-06-02 15:42:28 - TradingSystem - INFO - [OK] Retrieved 38 records for BTCUSD 1h
2025-06-02 15:46:25 - TradingSystem - INFO - [PATH] Config file: C:\Agent_Trading\Agent_Trading\config.json
2025-06-02 15:46:25 - TradingSystem - INFO - [PATH] Config db_path: crypto_market/data/crypto_trading.db
2025-06-02 15:46:25 - TradingSystem - INFO - [PATH] Resolved database path: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-06-02 15:46:25 - TradingSystem - INFO - [PATH] Database exists: True
2025-06-02 15:46:25 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-06-02 15:46:25 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-06-02 15:46:25 - TradingSystem - INFO - [OK] Delta Exchange client initialized
2025-06-02 15:46:25 - TradingSystem - INFO - [OK] Found BTCUSD product: ID 27, type: None
2025-06-02 15:46:25 - TradingSystem - INFO - [OK] Market data feed initialized - BTC Product ID: 27
2025-06-02 15:46:25 - TradingSystem - INFO - [PATH] Config file: C:\Agent_Trading\Agent_Trading\config.json
2025-06-02 15:46:25 - TradingSystem - INFO - [PATH] Config db_path: crypto_market/data/crypto_trading.db
2025-06-02 15:46:25 - TradingSystem - INFO - [PATH] Resolved database path: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-06-02 15:46:25 - TradingSystem - INFO - [PATH] Database exists: True
2025-06-02 15:46:25 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-06-02 15:46:25 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-06-02 15:46:25 - TradingSystem - INFO - [OK] Simple data fetcher initialized
2025-06-02 15:46:26 - TradingSystem - INFO - [PATH] Config file: C:\Agent_Trading\Agent_Trading\config.json
2025-06-02 15:46:26 - TradingSystem - INFO - [PATH] Config db_path: crypto_market/data/crypto_trading.db
2025-06-02 15:46:26 - TradingSystem - INFO - [PATH] Resolved database path: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-06-02 15:46:26 - TradingSystem - INFO - [PATH] Database exists: True
2025-06-02 15:46:26 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-06-02 15:46:26 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-06-02 15:46:26 - TradingSystem - INFO - [OK] Retrieved 762 records for BTCUSD 3m
2025-06-02 15:46:26 - TradingSystem - INFO - [OK] Retrieved 152 records for BTCUSD 15m
2025-06-02 15:46:26 - TradingSystem - INFO - [OK] Retrieved 38 records for BTCUSD 1h
2025-06-02 15:49:07 - TradingSystem - INFO - [PATH] Config file: C:\Agent_Trading\Agent_Trading\config.json
2025-06-02 15:49:07 - TradingSystem - INFO - [PATH] Config db_path: crypto_market/data/crypto_trading.db
2025-06-02 15:49:07 - TradingSystem - INFO - [PATH] Resolved database path: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-06-02 15:49:07 - TradingSystem - INFO - [PATH] Database exists: True
2025-06-02 15:49:07 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-06-02 15:49:07 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-06-02 15:49:07 - TradingSystem - INFO - [OK] Delta Exchange client initialized
2025-06-02 15:49:07 - TradingSystem - INFO - [OK] Found BTCUSD product: ID 27, type: None
2025-06-02 15:49:07 - TradingSystem - INFO - [OK] Market data feed initialized - BTC Product ID: 27
2025-06-02 15:49:07 - TradingSystem - INFO - [PATH] Config file: C:\Agent_Trading\Agent_Trading\config.json
2025-06-02 15:49:07 - TradingSystem - INFO - [PATH] Config db_path: crypto_market/data/crypto_trading.db
2025-06-02 15:49:07 - TradingSystem - INFO - [PATH] Resolved database path: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-06-02 15:49:07 - TradingSystem - INFO - [PATH] Database exists: True
2025-06-02 15:49:07 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-06-02 15:49:07 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-06-02 15:49:07 - TradingSystem - INFO - [OK] Simple data fetcher initialized
2025-06-02 15:49:07 - TradingSystem - INFO - [PATH] Config file: C:\Agent_Trading\Agent_Trading\config.json
2025-06-02 15:49:07 - TradingSystem - INFO - [PATH] Config db_path: crypto_market/data/crypto_trading.db
2025-06-02 15:49:07 - TradingSystem - INFO - [PATH] Resolved database path: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-06-02 15:49:07 - TradingSystem - INFO - [PATH] Database exists: True
2025-06-02 15:49:07 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-06-02 15:49:07 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-06-02 15:49:07 - TradingSystem - INFO - [OK] Retrieved 761 records for BTCUSD 3m
2025-06-02 15:49:07 - TradingSystem - INFO - [OK] Retrieved 152 records for BTCUSD 15m
2025-06-02 15:49:07 - TradingSystem - INFO - [OK] Retrieved 38 records for BTCUSD 1h
2025-06-02 15:49:10 - TradingSystem - INFO - [PATH] Config file: C:\Agent_Trading\Agent_Trading\config.json
2025-06-02 15:49:10 - TradingSystem - INFO - [PATH] Config db_path: crypto_market/data/crypto_trading.db
2025-06-02 15:49:10 - TradingSystem - INFO - [PATH] Resolved database path: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-06-02 15:49:10 - TradingSystem - INFO - [PATH] Database exists: True
2025-06-02 15:49:10 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-06-02 15:49:10 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-06-02 15:49:10 - TradingSystem - INFO - [OK] Delta Exchange client initialized
2025-06-02 15:49:10 - TradingSystem - INFO - [OK] Found BTCUSD product: ID 27, type: None
2025-06-02 15:49:10 - TradingSystem - INFO - [OK] Market data feed initialized - BTC Product ID: 27
2025-06-02 15:49:10 - TradingSystem - INFO - [PATH] Config file: C:\Agent_Trading\Agent_Trading\config.json
2025-06-02 15:49:10 - TradingSystem - INFO - [PATH] Config db_path: crypto_market/data/crypto_trading.db
2025-06-02 15:49:10 - TradingSystem - INFO - [PATH] Resolved database path: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-06-02 15:49:10 - TradingSystem - INFO - [PATH] Database exists: True
2025-06-02 15:49:10 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-06-02 15:49:10 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-06-02 15:49:10 - TradingSystem - INFO - [OK] Simple data fetcher initialized
2025-06-02 15:49:14 - TradingSystem - INFO - [PATH] Config file: C:\Agent_Trading\Agent_Trading\config.json
2025-06-02 15:49:14 - TradingSystem - INFO - [PATH] Config db_path: crypto_market/data/crypto_trading.db
2025-06-02 15:49:14 - TradingSystem - INFO - [PATH] Resolved database path: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-06-02 15:49:14 - TradingSystem - INFO - [PATH] Database exists: True
2025-06-02 15:49:14 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-06-02 15:49:14 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-06-02 15:49:14 - TradingSystem - INFO - [OK] Delta Exchange client initialized
2025-06-02 15:49:14 - TradingSystem - INFO - [OK] Found BTCUSD product: ID 27, type: None
2025-06-02 15:49:14 - TradingSystem - INFO - [OK] Market data feed initialized - BTC Product ID: 27
2025-06-02 15:49:14 - TradingSystem - INFO - [PATH] Config file: C:\Agent_Trading\Agent_Trading\config.json
2025-06-02 15:49:14 - TradingSystem - INFO - [PATH] Config db_path: crypto_market/data/crypto_trading.db
2025-06-02 15:49:14 - TradingSystem - INFO - [PATH] Resolved database path: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-06-02 15:49:14 - TradingSystem - INFO - [PATH] Database exists: True
2025-06-02 15:49:14 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-06-02 15:49:14 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-06-02 15:49:14 - TradingSystem - INFO - [OK] Simple data fetcher initialized
2025-06-02 15:49:16 - TradingSystem - INFO - [PATH] Config file: C:\Agent_Trading\Agent_Trading\config.json
2025-06-02 15:49:16 - TradingSystem - INFO - [PATH] Config db_path: crypto_market/data/crypto_trading.db
2025-06-02 15:49:16 - TradingSystem - INFO - [PATH] Resolved database path: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-06-02 15:49:16 - TradingSystem - INFO - [PATH] Database exists: True
2025-06-02 15:49:16 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-06-02 15:49:16 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-06-02 15:49:16 - TradingSystem - INFO - [OK] Delta Exchange client initialized
2025-06-02 15:49:17 - TradingSystem - INFO - [OK] Found BTCUSD product: ID 27, type: None
2025-06-02 15:49:17 - TradingSystem - INFO - [OK] Market data feed initialized - BTC Product ID: 27
2025-06-02 15:49:17 - TradingSystem - INFO - [PATH] Config file: C:\Agent_Trading\Agent_Trading\config.json
2025-06-02 15:49:17 - TradingSystem - INFO - [PATH] Config db_path: crypto_market/data/crypto_trading.db
2025-06-02 15:49:17 - TradingSystem - INFO - [PATH] Resolved database path: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-06-02 15:49:17 - TradingSystem - INFO - [PATH] Database exists: True
2025-06-02 15:49:17 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-06-02 15:49:17 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-06-02 15:49:17 - TradingSystem - INFO - [OK] Simple data fetcher initialized
2025-06-02 15:49:17 - TradingSystem - INFO - [PATH] Config file: C:\Agent_Trading\Agent_Trading\config.json
2025-06-02 15:49:17 - TradingSystem - INFO - [PATH] Config db_path: crypto_market/data/crypto_trading.db
2025-06-02 15:49:17 - TradingSystem - INFO - [PATH] Resolved database path: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-06-02 15:49:17 - TradingSystem - INFO - [PATH] Database exists: True
2025-06-02 15:49:17 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-06-02 15:49:17 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-06-02 15:49:17 - TradingSystem - INFO - [OK] Retrieved 761 records for BTCUSD 3m
2025-06-02 15:49:17 - TradingSystem - INFO - [OK] Retrieved 152 records for BTCUSD 15m
2025-06-02 15:49:17 - TradingSystem - INFO - [OK] Retrieved 38 records for BTCUSD 1h
2025-06-02 15:52:25 - TradingSystem - INFO - [PATH] Config file: C:\Agent_Trading\Agent_Trading\config.json
2025-06-02 15:52:25 - TradingSystem - INFO - [PATH] Config db_path: crypto_market/data/crypto_trading.db
2025-06-02 15:52:25 - TradingSystem - INFO - [PATH] Resolved database path: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-06-02 15:52:25 - TradingSystem - INFO - [PATH] Database exists: True
2025-06-02 15:52:25 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-06-02 15:52:25 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-06-02 15:52:25 - TradingSystem - INFO - [OK] Delta Exchange client initialized
2025-06-02 15:52:25 - TradingSystem - INFO - [OK] Found BTCUSD product: ID 27, type: None
2025-06-02 15:52:25 - TradingSystem - INFO - [OK] Market data feed initialized - BTC Product ID: 27
2025-06-02 15:52:25 - TradingSystem - INFO - [PATH] Config file: C:\Agent_Trading\Agent_Trading\config.json
2025-06-02 15:52:25 - TradingSystem - INFO - [PATH] Config db_path: crypto_market/data/crypto_trading.db
2025-06-02 15:52:25 - TradingSystem - INFO - [PATH] Resolved database path: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-06-02 15:52:25 - TradingSystem - INFO - [PATH] Database exists: True
2025-06-02 15:52:25 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-06-02 15:52:25 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-06-02 15:52:25 - TradingSystem - INFO - [OK] Simple data fetcher initialized
2025-06-02 15:52:29 - TradingSystem - INFO - [PATH] Config file: C:\Agent_Trading\Agent_Trading\config.json
2025-06-02 15:52:29 - TradingSystem - INFO - [PATH] Config db_path: crypto_market/data/crypto_trading.db
2025-06-02 15:52:29 - TradingSystem - INFO - [PATH] Resolved database path: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-06-02 15:52:29 - TradingSystem - INFO - [PATH] Database exists: True
2025-06-02 15:52:29 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-06-02 15:52:29 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-06-02 15:52:29 - TradingSystem - INFO - [OK] Delta Exchange client initialized
2025-06-02 15:52:29 - TradingSystem - INFO - [OK] Found BTCUSD product: ID 27, type: None
2025-06-02 15:52:29 - TradingSystem - INFO - [OK] Market data feed initialized - BTC Product ID: 27
2025-06-02 15:52:29 - TradingSystem - INFO - [PATH] Config file: C:\Agent_Trading\Agent_Trading\config.json
2025-06-02 15:52:29 - TradingSystem - INFO - [PATH] Config db_path: crypto_market/data/crypto_trading.db
2025-06-02 15:52:29 - TradingSystem - INFO - [PATH] Resolved database path: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-06-02 15:52:29 - TradingSystem - INFO - [PATH] Database exists: True
2025-06-02 15:52:29 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-06-02 15:52:29 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-06-02 15:52:29 - TradingSystem - INFO - [OK] Simple data fetcher initialized
2025-06-02 15:52:32 - TradingSystem - INFO - [PATH] Config file: C:\Agent_Trading\Agent_Trading\config.json
2025-06-02 15:52:32 - TradingSystem - INFO - [PATH] Config db_path: crypto_market/data/crypto_trading.db
2025-06-02 15:52:32 - TradingSystem - INFO - [PATH] Resolved database path: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-06-02 15:52:32 - TradingSystem - INFO - [PATH] Database exists: True
2025-06-02 15:52:32 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-06-02 15:52:32 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-06-02 15:52:32 - TradingSystem - INFO - [OK] Delta Exchange client initialized
2025-06-02 15:52:32 - TradingSystem - INFO - [OK] Found BTCUSD product: ID 27, type: None
2025-06-02 15:52:32 - TradingSystem - INFO - [OK] Market data feed initialized - BTC Product ID: 27
2025-06-02 15:52:32 - TradingSystem - INFO - [PATH] Config file: C:\Agent_Trading\Agent_Trading\config.json
2025-06-02 15:52:32 - TradingSystem - INFO - [PATH] Config db_path: crypto_market/data/crypto_trading.db
2025-06-02 15:52:32 - TradingSystem - INFO - [PATH] Resolved database path: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-06-02 15:52:32 - TradingSystem - INFO - [PATH] Database exists: True
2025-06-02 15:52:32 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-06-02 15:52:32 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-06-02 15:52:32 - TradingSystem - INFO - [OK] Simple data fetcher initialized
2025-06-02 15:52:32 - TradingSystem - INFO - [PATH] Config file: C:\Agent_Trading\Agent_Trading\config.json
2025-06-02 15:52:32 - TradingSystem - INFO - [PATH] Config db_path: crypto_market/data/crypto_trading.db
2025-06-02 15:52:32 - TradingSystem - INFO - [PATH] Resolved database path: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-06-02 15:52:32 - TradingSystem - INFO - [PATH] Database exists: True
2025-06-02 15:52:32 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-06-02 15:52:32 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-06-02 15:52:33 - TradingSystem - INFO - [OK] Retrieved 760 records for BTCUSD 3m
2025-06-02 15:52:33 - TradingSystem - INFO - [OK] Retrieved 152 records for BTCUSD 15m
2025-06-02 15:52:33 - TradingSystem - INFO - [OK] Retrieved 38 records for BTCUSD 1h
2025-06-02 15:52:34 - TradingSystem - INFO - [PATH] Config file: C:\Agent_Trading\Agent_Trading\config.json
2025-06-02 15:52:34 - TradingSystem - INFO - [PATH] Config db_path: crypto_market/data/crypto_trading.db
2025-06-02 15:52:34 - TradingSystem - INFO - [PATH] Resolved database path: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-06-02 15:52:34 - TradingSystem - INFO - [PATH] Database exists: True
2025-06-02 15:52:34 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-06-02 15:52:34 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-06-02 15:52:34 - TradingSystem - INFO - [OK] Delta Exchange client initialized
2025-06-02 15:52:34 - TradingSystem - INFO - [OK] Found BTCUSD product: ID 27, type: None
2025-06-02 15:52:34 - TradingSystem - INFO - [OK] Market data feed initialized - BTC Product ID: 27
2025-06-02 15:52:34 - TradingSystem - INFO - [PATH] Config file: C:\Agent_Trading\Agent_Trading\config.json
2025-06-02 15:52:34 - TradingSystem - INFO - [PATH] Config db_path: crypto_market/data/crypto_trading.db
2025-06-02 15:52:34 - TradingSystem - INFO - [PATH] Resolved database path: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-06-02 15:52:34 - TradingSystem - INFO - [PATH] Database exists: True
2025-06-02 15:52:34 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-06-02 15:52:34 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-06-02 15:52:34 - TradingSystem - INFO - [OK] Simple data fetcher initialized
2025-06-02 15:53:11 - TradingSystem - INFO - [PATH] Config file: C:\Agent_Trading\Agent_Trading\config.json
2025-06-02 15:53:11 - TradingSystem - INFO - [PATH] Config db_path: crypto_market/data/crypto_trading.db
2025-06-02 15:53:11 - TradingSystem - INFO - [PATH] Resolved database path: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-06-02 15:53:11 - TradingSystem - INFO - [PATH] Database exists: True
2025-06-02 15:53:11 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-06-02 15:53:11 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-06-02 15:53:11 - TradingSystem - INFO - [OK] Delta Exchange client initialized
2025-06-02 15:53:12 - TradingSystem - INFO - [OK] Found BTCUSD product: ID 27, type: None
2025-06-02 15:53:12 - TradingSystem - INFO - [OK] Market data feed initialized - BTC Product ID: 27
2025-06-02 15:53:12 - TradingSystem - INFO - [PATH] Config file: C:\Agent_Trading\Agent_Trading\config.json
2025-06-02 15:53:12 - TradingSystem - INFO - [PATH] Config db_path: crypto_market/data/crypto_trading.db
2025-06-02 15:53:12 - TradingSystem - INFO - [PATH] Resolved database path: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-06-02 15:53:12 - TradingSystem - INFO - [PATH] Database exists: True
2025-06-02 15:53:12 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-06-02 15:53:12 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-06-02 15:53:12 - TradingSystem - INFO - [OK] Simple data fetcher initialized
2025-06-02 15:53:15 - TradingSystem - INFO - [PATH] Config file: C:\Agent_Trading\Agent_Trading\config.json
2025-06-02 15:53:15 - TradingSystem - INFO - [PATH] Config db_path: crypto_market/data/crypto_trading.db
2025-06-02 15:53:15 - TradingSystem - INFO - [PATH] Resolved database path: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-06-02 15:53:15 - TradingSystem - INFO - [PATH] Database exists: True
2025-06-02 15:53:15 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-06-02 15:53:15 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-06-02 15:53:15 - TradingSystem - INFO - [OK] Delta Exchange client initialized
2025-06-02 15:53:15 - TradingSystem - INFO - [OK] Found BTCUSD product: ID 27, type: None
2025-06-02 15:53:15 - TradingSystem - INFO - [OK] Market data feed initialized - BTC Product ID: 27
2025-06-02 15:53:15 - TradingSystem - INFO - [PATH] Config file: C:\Agent_Trading\Agent_Trading\config.json
2025-06-02 15:53:15 - TradingSystem - INFO - [PATH] Config db_path: crypto_market/data/crypto_trading.db
2025-06-02 15:53:15 - TradingSystem - INFO - [PATH] Resolved database path: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-06-02 15:53:15 - TradingSystem - INFO - [PATH] Database exists: True
2025-06-02 15:53:15 - TradingSystem - INFO - [OK] Database schema initialized successfully
2025-06-02 15:53:15 - TradingSystem - INFO - [OK] Database manager initialized: C:\Agent_Trading\Agent_Trading\crypto_market\data\crypto_trading.db
2025-06-02 15:53:15 - TradingSystem - INFO - [OK] Simple data fetcher initialized
