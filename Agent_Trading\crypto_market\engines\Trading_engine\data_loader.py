"""
Trading Engine Data Loader
==========================

Specialized data loading utilities for the trading engine and backtesting.
Provides clean, optimized data loading functions for VectorBT backtesting.
"""

import pandas as pd
import numpy as np
from datetime import datetime, timed<PERSON><PERSON>
from typing import Dict, List, Optional, Tuple
import sys
from pathlib import Path

# Add project paths
project_root = Path(__file__).parent.parent.parent.parent
sys.path.insert(0, str(project_root))

from core.config_manager import ConfigManager
from crypto_market.engines.data_engine.database_manager import DatabaseManager
from crypto_market.engines.data_engine.data_validator import DataValidator
from crypto_market.engines.data_engine.data_normalizer import DataNormalizer

class TradingDataLoader:
    """Professional data loader for trading engine"""
    
    def __init__(self, config_path: Optional[str] = None):
        """Initialize trading data loader
        
        Args:
            config_path: Path to config file (optional)
        """
        
        if config_path is None:
            config_path = str(project_root / "config.json")
        
        self.config = ConfigManager(config_path)
        self.db_manager = DatabaseManager(self.config)
        self.validator = DataValidator()
        self.normalizer = DataNormalizer()
        
    def load_multi_timeframe_data(
        self, 
        symbol: str = "BTCUSD",
        timeframes: List[str] = None,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        validate_data: bool = True,
        normalize_data: bool = False
    ) -> Dict[str, pd.DataFrame]:
        """Load multi-timeframe data for trading/backtesting
        
        Args:
            symbol: Trading symbol
            timeframes: List of timeframes to load
            start_date: Start date (optional)
            end_date: End date (optional)
            validate_data: Whether to validate data quality
            normalize_data: Whether to normalize data
            
        Returns:
            Dictionary with timeframe as key and DataFrame as value
        """
        
        if timeframes is None:
            timeframes = ['3m', '15m', '1h']
        
        print(f"📊 Loading multi-timeframe data for {symbol}")
        print(f"   Timeframes: {timeframes}")
        if start_date:
            print(f"   Start: {start_date}")
        if end_date:
            print(f"   End: {end_date}")
        
        data = {}
        
        for timeframe in timeframes:
            print(f"   Loading {timeframe} data...")
            
            # Load data from database
            df = self.db_manager.get_market_data(
                symbol=symbol,
                timeframe=timeframe,
                start_date=start_date,
                end_date=end_date
            )
            
            if df.empty:
                print(f"   ❌ No {timeframe} data found")
                continue
            
            # Ensure proper datetime index
            df = self._prepare_dataframe(df)
            
            # Validate data quality if requested
            if validate_data:
                df = self._validate_and_clean_data(df, timeframe)
            
            # Normalize data if requested
            if normalize_data:
                df = self.normalizer.normalize_ohlcv_data(df)
            
            data[timeframe] = df
            print(f"   ✅ {timeframe}: {len(df):,} candles ({df.index[0]} to {df.index[-1]})")
        
        print(f"✅ Loaded {len(data)} timeframes successfully")
        return data
    
    def load_backtest_data(
        self,
        symbol: str = "BTCUSD",
        days: Optional[int] = None,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        sample_size: Optional[int] = None
    ) -> Tuple[pd.DataFrame, pd.DataFrame, pd.DataFrame]:
        """Load data specifically formatted for backtesting
        
        Args:
            symbol: Trading symbol
            days: Number of recent days to load
            start_date: Start date (optional)
            end_date: End date (optional)
            sample_size: Number of recent candles to sample (for testing)
            
        Returns:
            Tuple of (df_3m, df_15m, df_1h)
        """
        
        print(f"🚀 Loading backtest data for {symbol}")
        
        # Determine date range
        if days:
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days)
            print(f"   Using last {days} days: {start_date} to {end_date}")
        elif start_date and end_date:
            print(f"   Using date range: {start_date} to {end_date}")
        else:
            print(f"   Using all available data")
        
        # Load multi-timeframe data
        data = self.load_multi_timeframe_data(
            symbol=symbol,
            timeframes=['3m', '15m', '1h'],
            start_date=start_date,
            end_date=end_date,
            validate_data=True,
            normalize_data=False
        )
        
        # Extract individual timeframes
        df_3m = data.get('3m', pd.DataFrame())
        df_15m = data.get('15m', pd.DataFrame())
        df_1h = data.get('1h', pd.DataFrame())
        
        # Apply sampling if requested (for testing)
        if sample_size:
            print(f"   Applying sample size: {sample_size} candles")
            df_3m = df_3m.tail(sample_size) if not df_3m.empty else df_3m
            
            # Calculate corresponding samples for other timeframes
            if not df_15m.empty:
                sample_15m = sample_size // 5  # 3m to 15m ratio
                df_15m = df_15m.tail(max(1, sample_15m))
            
            if not df_1h.empty:
                sample_1h = sample_size // 20  # 3m to 1h ratio
                df_1h = df_1h.tail(max(1, sample_1h))
        
        # Validate we have data
        if df_3m.empty:
            raise ValueError("No 3m data available for backtesting")
        if df_15m.empty:
            raise ValueError("No 15m data available for backtesting")
        if df_1h.empty:
            raise ValueError("No 1h data available for backtesting")
        
        print(f"✅ Backtest data ready:")
        print(f"   3m: {len(df_3m):,} candles")
        print(f"   15m: {len(df_15m):,} candles")
        print(f"   1h: {len(df_1h):,} candles")
        
        return df_3m, df_15m, df_1h
    
    def load_recent_data(self, days: int = 30) -> Tuple[pd.DataFrame, pd.DataFrame, pd.DataFrame]:
        """Load recent data for quick testing
        
        Args:
            days: Number of recent days to load
            
        Returns:
            Tuple of (df_3m, df_15m, df_1h)
        """
        return self.load_backtest_data(days=days)
    
    def load_sample_data(self, candles_3m: int = 10000) -> Tuple[pd.DataFrame, pd.DataFrame, pd.DataFrame]:
        """Load sample data for fast testing
        
        Args:
            candles_3m: Number of 3m candles to sample
            
        Returns:
            Tuple of (df_3m, df_15m, df_1h)
        """
        return self.load_backtest_data(sample_size=candles_3m)
    
    def get_data_summary(self, symbol: str = "BTCUSD") -> Dict:
        """Get summary of available data
        
        Args:
            symbol: Trading symbol
            
        Returns:
            Dictionary with data summary
        """
        
        print(f"📊 Getting data summary for {symbol}")
        
        summary = {}
        timeframes = ['3m', '15m', '1h']
        
        for timeframe in timeframes:
            count = self.db_manager.get_record_count(symbol, timeframe)
            
            if count > 0:
                # Get date range
                df_sample = self.db_manager.get_market_data(
                    symbol=symbol,
                    timeframe=timeframe,
                    limit=1
                )
                df_sample_end = self.db_manager.get_market_data(
                    symbol=symbol,
                    timeframe=timeframe,
                    limit=1,
                    order_desc=True
                )
                
                if not df_sample.empty and not df_sample_end.empty:
                    start_date = df_sample.index[0]
                    end_date = df_sample_end.index[0]
                    
                    summary[timeframe] = {
                        'count': count,
                        'start_date': start_date,
                        'end_date': end_date,
                        'days': (end_date - start_date).days
                    }
                else:
                    summary[timeframe] = {'count': count}
            else:
                summary[timeframe] = {'count': 0}
        
        # Print summary
        print(f"✅ Data Summary:")
        for tf, info in summary.items():
            if info['count'] > 0 and 'start_date' in info:
                print(f"   {tf}: {info['count']:,} records ({info['start_date']} to {info['end_date']}, {info['days']} days)")
            else:
                print(f"   {tf}: {info['count']} records")
        
        return summary
    
    def _prepare_dataframe(self, df: pd.DataFrame) -> pd.DataFrame:
        """Prepare DataFrame for trading engine use
        
        Args:
            df: Input DataFrame
            
        Returns:
            Prepared DataFrame
        """
        
        # Ensure proper datetime index
        if 'timestamp' in df.columns:
            df.set_index('timestamp', inplace=True)
        
        # Convert index to datetime
        df.index = pd.to_datetime(df.index)
        
        # Sort by timestamp
        df.sort_index(inplace=True)
        
        # Ensure required columns exist
        required_columns = ['open', 'high', 'low', 'close', 'volume']
        for col in required_columns:
            if col not in df.columns:
                raise ValueError(f"Required column '{col}' not found in data")
        
        # Convert to numeric
        for col in required_columns:
            df[col] = pd.to_numeric(df[col], errors='coerce')
        
        # Remove any rows with NaN values
        df.dropna(inplace=True)
        
        return df
    
    def _validate_and_clean_data(self, df: pd.DataFrame, timeframe: str) -> pd.DataFrame:
        """Validate and clean data
        
        Args:
            df: Input DataFrame
            timeframe: Timeframe string
            
        Returns:
            Cleaned DataFrame
        """
        
        original_count = len(df)
        
        # Validate OHLCV relationships
        df = df[df['high'] >= df['low']]
        df = df[df['high'] >= df['open']]
        df = df[df['high'] >= df['close']]
        df = df[df['low'] <= df['open']]
        df = df[df['low'] <= df['close']]
        df = df[df['volume'] >= 0]
        
        # Remove extreme outliers (price changes > 20%)
        df['price_change'] = df['close'].pct_change().abs()
        df = df[df['price_change'] <= 0.2]
        df.drop('price_change', axis=1, inplace=True)
        
        cleaned_count = len(df)
        removed_count = original_count - cleaned_count
        
        if removed_count > 0:
            print(f"   🧹 Cleaned {timeframe}: removed {removed_count} invalid records ({removed_count/original_count:.2%})")
        
        return df

# Convenience functions for easy use (lazy initialization to prevent import hanging)
def load_backtest_data(days: Optional[int] = None, sample_size: Optional[int] = None) -> Tuple[pd.DataFrame, pd.DataFrame, pd.DataFrame]:
    """Quick function to load backtest data

    Args:
        days: Number of recent days to load
        sample_size: Number of 3m candles to sample

    Returns:
        Tuple of (df_3m, df_15m, df_1h)
    """
    # Lazy initialization to prevent hanging during import
    try:
        loader = TradingDataLoader()

        if sample_size:
            return loader.load_sample_data(sample_size)
        elif days:
            return loader.load_recent_data(days)
        else:
            return loader.load_backtest_data()
    except Exception as e:
        print(f"⚠️ Data loading failed: {e}")
        # Return empty DataFrames as fallback
        import pandas as pd
        empty_df = pd.DataFrame()
        return empty_df, empty_df, empty_df

def get_data_summary() -> Dict:
    """Quick function to get data summary

    Returns:
        Dictionary with data summary
    """
    # Lazy initialization to prevent hanging during import
    try:
        loader = TradingDataLoader()
        return loader.get_data_summary()
    except Exception as e:
        print(f"⚠️ Data summary failed: {e}")
        return {}
